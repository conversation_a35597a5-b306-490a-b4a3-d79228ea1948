<?php $__env->startSection('title', 'تفاصيل الطلب'); ?>

<?php $__env->startSection('styles'); ?>
<style>
    /* تصميم مطابق للصورة المرفقة */
    body {
        font-family: 'Cairo', 'Tahoma', 'Arial', sans-serif;
        background: #f8f9fa;
        direction: rtl;
        margin: 0;
        padding: 20px;
    }
    
    .order-container {
        max-width: 900px;
        margin: 0 auto;
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        overflow: hidden;
    }
    
    /* رأس الفاتورة */
    .order-header {
        background: white;
        padding: 20px;
        border-bottom: 3px solid #000;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .company-section {
        display: flex;
        align-items: center;
        gap: 15px;
    }
    
    .company-logo {
        width: 60px;
        height: 60px;
        background: #e74c3c;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        font-size: 20px;
    }
    
    .company-info h2 {
        margin: 0;
        font-size: 18px;
        font-weight: bold;
        color: #333;
    }
    
    .company-info p {
        margin: 5px 0 0 0;
        font-size: 14px;
        color: #666;
    }
    
    .order-info {
        text-align: left;
        background: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
        border: 1px solid #ddd;
    }
    
    .order-info h3 {
        margin: 0 0 5px 0;
        font-size: 16px;
        font-weight: bold;
        color: #333;
    }
    
    .order-info p {
        margin: 0;
        font-size: 14px;
        color: #666;
    }
    
    /* جدول التفاصيل */
    .details-section {
        padding: 0;
    }
    
    .section-header {
        background: #f1f3f4;
        padding: 12px 20px;
        font-weight: bold;
        font-size: 14px;
        color: #333;
        border-bottom: 1px solid #ddd;
        text-align: right;
    }
    
    .details-table {
        width: 100%;
        border-collapse: collapse;
        margin: 0;
    }
    
    .details-table tr {
        border-bottom: 1px solid #eee;
    }
    
    .details-table tr:nth-child(even) {
        background: #f9f9f9;
    }
    
    .details-table td {
        padding: 12px 20px;
        font-size: 14px;
        vertical-align: middle;
    }
    
    .label-cell {
        font-weight: 600;
        color: #333;
        width: 25%;
        text-align: right;
        background: #f8f9fa;
    }
    
    .value-cell {
        color: #555;
        width: 25%;
        text-align: right;
    }
    
    /* قسم التوقيعات */
    .signatures-section {
        padding: 30px 20px;
        display: flex;
        justify-content: space-between;
        gap: 50px;
    }
    
    .signature-box {
        flex: 1;
        text-align: center;
    }
    
    .signature-box h4 {
        margin: 0 0 30px 0;
        font-size: 14px;
        font-weight: 600;
        color: #333;
    }
    
    .signature-line {
        border-bottom: 2px solid #000;
        height: 50px;
        margin-bottom: 10px;
    }
    
    .signature-date {
        font-size: 12px;
        color: #666;
    }
    
    /* تذييل الشركة */
    .company-footer {
        background: #f8f9fa;
        padding: 20px;
        text-align: center;
        border-top: 1px solid #ddd;
    }
    
    .company-footer h3 {
        margin: 0 0 5px 0;
        font-size: 16px;
        font-weight: bold;
        color: #333;
    }
    
    .company-footer p {
        margin: 5px 0;
        font-size: 14px;
        color: #666;
    }
    
    .company-footer small {
        display: block;
        margin-top: 15px;
        font-size: 12px;
        color: #999;
        border-top: 1px solid #ddd;
        padding-top: 10px;
    }
    
    /* أزرار العمليات */
    .action-buttons {
        background: white;
        padding: 20px;
        margin-bottom: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        text-align: center;
    }
    
    .btn {
        padding: 10px 20px;
        margin: 0 5px;
        border: none;
        border-radius: 5px;
        font-size: 14px;
        cursor: pointer;
        text-decoration: none;
        display: inline-block;
    }
    
    .btn-primary {
        background: #007bff;
        color: white;
    }
    
    .btn-warning {
        background: #ffc107;
        color: #212529;
    }
    
    .btn-info {
        background: #17a2b8;
        color: white;
    }
    
    .btn-secondary {
        background: #6c757d;
        color: white;
    }
    
    .btn:hover {
        opacity: 0.9;
        transform: translateY(-1px);
    }
    
    /* حالة الطلب */
    .status-badge {
        display: inline-block;
        padding: 5px 12px;
        border-radius: 15px;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
    }
    
    .status-pending {
        background: #fff3cd;
        color: #856404;
        border: 1px solid #ffeaa7;
    }
    
    .status-completed {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }
    
    .status-cancelled {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }
    
    /* إعدادات الطباعة */
    @media print {
        @page {
            size: A4 portrait;
            margin: 15mm 10mm;
        }
        
        body {
            background: white !important;
            font-size: 12px !important;
        }
        
        .action-buttons {
            display: none !important;
        }
        
        .order-container {
            box-shadow: none !important;
            border-radius: 0 !important;
        }
        
        .order-header {
            border-bottom: 3px solid #000 !important;
        }
        
        .details-table {
            border: 1px solid #000 !important;
        }
        
        .details-table td {
            border: 1px solid #000 !important;
        }
        
        .section-header {
            background: #f0f0f0 !important;
            border: 1px solid #000 !important;
        }
        
        .signatures-section {
            page-break-inside: avoid !important;
        }
        
        .company-footer {
            page-break-inside: avoid !important;
        }
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="page-container">
    <!-- أزرار العمليات -->
    <div class="action-buttons no-print">
        <a href="<?php echo e(route('orders.index')); ?>" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة للقائمة
        </a>
        <a href="<?php echo e(route('orders.edit', $order)); ?>" class="btn btn-warning">
            <i class="fas fa-edit"></i> تعديل
        </a>
        <button class="btn btn-primary" onclick="window.print()">
            <i class="fas fa-print"></i> طباعة
        </button>
    </div>

    <!-- حاوية الطلب -->
    <div class="order-container">
        <!-- رأس الطلب -->
        <div class="order-header">
            <div class="company-section">
                <div class="company-logo">
                    شعار
                </div>
                <div class="company-info">
                    <h2>مجموعة أبراهيم الاحمدي اليمنية</h2>
                    <p>للتجارة والخدمات اللوجستية</p>
                </div>
            </div>
            <div class="order-info">
                <h3>Order #<?php echo e($order->branch_serial ?? 'MKL25-' . $order->id); ?></h3>
                <p><?php echo e($order->created_at->format('Y/m/d')); ?></p>
            </div>
        </div>

        <!-- قسم معلومات الطلب -->
        <div class="details-section">
            <div class="section-header">معلومات الطلب</div>
            <table class="details-table">
                <tr>
                    <td class="label-cell">رقم الطلب</td>
                    <td class="value-cell"><?php echo e($order->branch_serial ?? 'MKL25-' . $order->id); ?></td>
                    <td class="label-cell">تاريخ الطلب</td>
                    <td class="value-cell"><?php echo e($order->created_at->format('Y-m-d') ?? '00:00:00 2025-06-18'); ?></td>
                </tr>
                <tr>
                    <td class="label-cell">رقم الهاتف</td>
                    <td class="value-cell"><?php echo e($order->phone_number ?? '712588309'); ?></td>
                    <td class="label-cell">نوع الخدمة</td>
                    <td class="value-cell">شحن جوي</td>
                </tr>
                <tr>
                    <td class="label-cell">اسم العميل</td>
                    <td class="value-cell"><?php echo e($order->customer_name ?? 'محمد عبدالله علي أحمد'); ?></td>
                    <td class="label-cell">التوصيل</td>
                    <td class="value-cell">محمد عبدالله علي أحمد</td>
                </tr>
                <tr>
                    <td class="label-cell">حالة الطلب</td>
                    <td class="value-cell">
                        <span class="status-badge status-pending">قيد التنفيذ</span>
                    </td>
                    <td class="label-cell"></td>
                    <td class="value-cell"></td>
                </tr>
            </table>
        </div>

        <!-- قسم تفاصيل البضاعة -->
        <div class="details-section">
            <div class="section-header">تفاصيل البضاعة</div>
            <table class="details-table">
                <tr>
                    <td class="label-cell">نوع البضاعة</td>
                    <td class="value-cell"><?php echo e($order->goods_name ?? 'جوال خضروات'); ?></td>
                    <td class="label-cell">بلد المنشأ</td>
                    <td class="value-cell">اليمن</td>
                </tr>
                <tr>
                    <td class="label-cell">الكمية</td>
                    <td class="value-cell"><?php echo e($order->quantity ?? '15 كرتون'); ?></td>
                    <td class="label-cell">منطقة التسليم</td>
                    <td class="value-cell">صنعاء</td>
                </tr>
                <tr>
                    <td class="label-cell">الوزن</td>
                    <td class="value-cell"><?php echo e($order->weight ?? '10000.00'); ?></td>
                    <td class="label-cell">منطقة العبور</td>
                    <td class="value-cell">صنعاء</td>
                </tr>
            </table>
        </div>

        <!-- قسم التفاصيل المالية -->
        <div class="details-section">
            <div class="section-header">التفاصيل المالية</div>
            <table class="details-table">
                <tr>
                    <td class="label-cell">سعر الخدمة</td>
                    <td class="value-cell"><?php echo e(number_format($order->selling_price ?? 0, 2)); ?> ريال</td>
                    <td class="label-cell">اجمالي المبلغ</td>
                    <td class="value-cell"><?php echo e(number_format($order->selling_price ?? 0, 2)); ?> ريال</td>
                </tr>
                <tr>
                    <td class="label-cell">مبلغ ضريبة قيمة مضافة</td>
                    <td class="value-cell">غير محدد</td>
                    <td class="label-cell">اجمالي المبلغ</td>
                    <td class="value-cell">غير محدد</td>
                </tr>
                <tr>
                    <td class="label-cell">مبلغ ضريبة قيمة مضافة</td>
                    <td class="value-cell">غير محدد</td>
                    <td class="label-cell">تصريح لفرق</td>
                    <td class="value-cell">غير محدد</td>
                </tr>
                <tr style="background: #e8f5e8;">
                    <td class="label-cell" style="font-weight: bold;">عمولة</td>
                    <td class="value-cell" style="font-weight: bold; color: #28a745;"><?php echo e(number_format($order->selling_price ?? 0, 2)); ?> ريال</td>
                    <td class="label-cell" style="font-weight: bold;">صافي الربح</td>
                    <td class="value-cell" style="font-weight: bold; color: #28a745;"><?php echo e(number_format(($order->selling_price ?? 0) - ($order->purchase_price ?? 0), 2)); ?> ريال</td>
                </tr>
            </table>
        </div>

        <!-- قسم الملاحظات -->
        <div class="details-section">
            <div class="section-header">ملاحظات</div>
            <div style="padding: 20px; min-height: 60px; background: #f9f9f9;">
                <?php echo e($order->notes ?? 'لا توجد ملاحظات'); ?>

            </div>
        </div>

        <!-- قسم التوقيعات -->
        <div class="signatures-section">
            <div class="signature-box">
                <h4>توقيع سعر امتيازية العميل</h4>
                <div class="signature-line"></div>
                <div class="signature-date">محمد عبدالله علي أحمد</div>
            </div>
            <div style="text-align: center; padding: 50px 0;">
                <div style="width: 80px; height: 80px; border: 2px dashed #ccc; border-radius: 50%; margin: 0 auto; display: flex; align-items: center; justify-content: center; color: #999;">
                    ختم الشركة
                </div>
            </div>
            <div class="signature-box">
                <h4>توقيع العميل</h4>
                <div class="signature-line"></div>
                <div class="signature-date">موقع العميل</div>
            </div>
        </div>

        <!-- تذييل الشركة -->
        <div class="company-footer">
            <h3>مجموعة أبراهيم الاحمدي اليمنية للتجارة والخدمات اللوجستية</h3>
            <p>شركة التقديم بنا</p>
            <p>البريد الإلكتروني: <EMAIL> | الهاتف: *********</p>
            <p>اليمن - صنعاء - محافظة صنعاء</p>
            <small>تم إنشاء هذا المستند في <?php echo e(now()->format('H:i:s d-m-Y')); ?></small>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp2\htdocs\app\resources\views/orders/show.blade.php ENDPATH**/ ?>