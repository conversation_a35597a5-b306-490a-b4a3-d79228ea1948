<?php $__env->startSection('title', 'تفاصيل الطلب'); ?>

<?php $__env->startSection('styles'); ?>
<style>
    /* إعدادات الطباعة */
    @media print {
        @page {
            size: A4 portrait;
            margin: 15mm;
        }
        
        * {
            -webkit-print-color-adjust: exact !important;
            color-adjust: exact !important;
            print-color-adjust: exact !important;
        }
        
        body {
            font-family: 'Cairo', 'Tahoma', 'Arial', sans-serif !important;
            font-size: 14px !important;
            line-height: 1.5 !important;
            direction: rtl !important;
            text-align: right !important;
            background: white !important;
            color: black !important;
        }
        
        .no-print, .action-buttons, .navbar, .sidebar, footer, nav, .btn {
            display: none !important;
        }
        
        .invoice-container {
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
            background: white !important;
            box-shadow: none !important;
            border-radius: 0 !important;
            margin: 0 !important;
            padding: 0 !important;
        }
        
        .details-table {
            width: 100% !important;
            border-collapse: collapse !important;
            border: 2px solid black !important;
        }
        
        .details-table td {
            border: 1px solid black !important;
            padding: 8px !important;
            background: white !important;
            color: black !important;
        }
        
        .invoice-header {
            background: white !important;
            color: black !important;
            border-bottom: 3px solid black !important;
            padding: 15px !important;
        }
    }

    /* أنماط الشاشة */
    @media screen {
        body {
            font-family: 'Cairo', 'Segoe UI', 'Tahoma', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            background-attachment: fixed;
            direction: rtl;
            text-align: right;
            margin: 0;
            padding: 20px;
            font-size: 14px;
            line-height: 1.6;
            min-height: 100vh;
        }

        .page-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .action-buttons {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            padding: 2rem;
            border-radius: 20px;
            box-shadow: 0 15px 50px rgba(0, 0, 0, 0.15);
            margin-bottom: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .invoice-container {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.2);
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .invoice-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .company-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .company-logo {
            width: 60px;
            height: 60px;
            border-radius: 10px;
            background: white;
            padding: 5px;
        }

        .company-name {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .company-subtitle {
            font-size: 1rem;
            opacity: 0.9;
        }

        .invoice-details {
            text-align: center;
            background: rgba(255, 255, 255, 0.2);
            padding: 1rem;
            border-radius: 10px;
        }

        .invoice-number {
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .details-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            margin: 2rem;
            font-size: 15px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 15px 50px rgba(0, 0, 0, 0.15);
            background: white;
        }

        .details-table td {
            border: 1px solid #e2e8f0;
            padding: 1rem;
            vertical-align: top;
        }

        .label-col {
            background: #f8fafc;
            font-weight: 600;
            color: #2d3748;
            width: 25%;
        }

        .value-col {
            background: white;
            color: #4a5568;
            width: 25%;
        }

        .section-title {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: bold;
            text-align: center;
            padding: 1rem;
            font-size: 1.1rem;
        }

        .btn {
            border-radius: 12px;
            padding: 0.8rem 1.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-warning {
            background: #ffc107;
            color: #212529;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="page-container">
    <!-- أزرار الإجراءات -->
    <div class="action-buttons no-print">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h4 class="mb-0">تفاصيل الطلب: <?php echo e($order->branch_serial ?? $order->order_number ?? '#' . $order->id); ?></h4>
                <small class="text-muted">تم الإنشاء في: <?php echo e($order->created_at->format('Y-m-d H:i')); ?></small>
            </div>
            <div class="d-flex gap-2">
                <a href="<?php echo e(route('orders.index')); ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i> العودة للقائمة
                </a>
                <a href="<?php echo e(route('orders.edit', $order)); ?>" class="btn btn-warning">
                    <i class="fas fa-edit"></i> تعديل
                </a>
                <button class="btn btn-primary" onclick="window.print()">
                    <i class="fas fa-print"></i> طباعة
                </button>
            </div>
        </div>
    </div>

    <!-- حاوية الفاتورة -->
    <div class="invoice-container">
        <!-- رأس الفاتورة -->
        <div class="invoice-header">
            <div class="company-info">
                <img src="<?php echo e(asset('images/logos/aswsd.png')); ?>" alt="شعار الشركة" class="company-logo">
                <div class="company-text">
                    <div class="company-name">مجموعة أبراهيم الاحمدي اليمنية</div>
                    <div class="company-subtitle">للتجارة والخدمات اللوجستية</div>
                </div>
            </div>
            <div class="invoice-details">
                <div class="invoice-number">طلب #<?php echo e($order->branch_serial ?? $order->order_number ?? $order->id); ?></div>
                <div class="invoice-date"><?php echo e($order->created_at->format('Y/m/d')); ?></div>
            </div>
        </div>

        <!-- جدول تفاصيل الطلب -->
        <table class="details-table">
            <tr>
                <td colspan="4" class="section-title">معلومات الطلب الأساسية</td>
            </tr>
            <tr>
                <td class="label-col">رقم الطلب</td>
                <td class="value-col"><?php echo e($order->branch_serial ?? $order->order_number ?? $order->id); ?></td>
                <td class="label-col">التاريخ</td>
                <td class="value-col"><?php echo e($order->request_date ?? $order->created_at->format('Y-m-d')); ?></td>
            </tr>
            <tr>
                <td class="label-col">اسم العميل</td>
                <td class="value-col"><?php echo e($order->customer_name ?? 'غير محدد'); ?></td>
                <td class="label-col">رقم الهاتف</td>
                <td class="value-col"><?php echo e($order->phone_number ?? 'غير محدد'); ?></td>
            </tr>
            <tr>
                <td class="label-col">اسم المستلم</td>
                <td class="value-col"><?php echo e($order->recipient_name ?? 'غير محدد'); ?></td>
                <td class="label-col">نوع الخدمة</td>
                <td class="value-col"><?php echo e($order->service_type ?? 'غير محدد'); ?></td>
            </tr>

            <tr>
                <td colspan="4" class="section-title">تفاصيل البضاعة</td>
            </tr>
            <tr>
                <td class="label-col">نوع البضاعة</td>
                <td class="value-col"><?php echo e($order->goods_name ?? 'غير محدد'); ?></td>
                <td class="label-col">بلد المنشأ</td>
                <td class="value-col"><?php echo e($order->country_of_origin ?? 'غير محدد'); ?></td>
            </tr>
            <tr>
                <td class="label-col">الوزن</td>
                <td class="value-col"><?php echo e($order->weight ?? 'غير محدد'); ?></td>
                <td class="label-col">الكمية</td>
                <td class="value-col"><?php echo e($order->quantity ?? 'غير محدد'); ?></td>
            </tr>
            <tr>
                <td class="label-col">منطقة المغادرة</td>
                <td class="value-col"><?php echo e($order->departure_area ?? 'غير محدد'); ?></td>
                <td class="label-col">منطقة التسليم</td>
                <td class="value-col"><?php echo e($order->delivery_area ?? 'غير محدد'); ?></td>
            </tr>

            <tr>
                <td colspan="4" class="section-title">التكاليف والأسعار</td>
            </tr>
            <tr>
                <td class="label-col">سعر الشراء</td>
                <td class="value-col"><?php echo e(number_format($order->purchase_price ?? 0, 2)); ?> ريال</td>
                <td class="label-col">سعر البيع</td>
                <td class="value-col"><?php echo e(number_format($order->selling_price ?? 0, 2)); ?> ريال</td>
            </tr>
            <tr>
                <td class="label-col">الربح</td>
                <td class="value-col"><?php echo e(number_format(($order->selling_price ?? 0) - ($order->purchase_price ?? 0), 2)); ?> ريال</td>
                <td class="label-col">حالة الطلب</td>
                <td class="value-col"><?php echo e($order->status ?? 'غير محدد'); ?></td>
            </tr>

            <?php if($order->notes): ?>
            <tr>
                <td class="label-col">ملاحظات</td>
                <td colspan="3" class="value-col"><?php echo e($order->notes); ?></td>
            </tr>
            <?php endif; ?>
        </table>

        <!-- قسم التوقيعات -->
        <div style="display: flex; justify-content: space-between; margin: 2rem; gap: 2rem;">
            <div style="flex: 1; text-align: center; padding: 1rem; background: #f8f9fa; border-radius: 10px;">
                <h5>توقيع العميل</h5>
                <div style="border-bottom: 2px dashed #ccc; height: 60px; margin: 1rem 0;"></div>
                <small>التاريخ: ___________</small>
            </div>
            <div style="flex: 1; text-align: center; padding: 1rem; background: #f8f9fa; border-radius: 10px;">
                <h5>توقيع المسؤول</h5>
                <div style="border-bottom: 2px dashed #ccc; height: 60px; margin: 1rem 0;"></div>
                <small>التاريخ: ___________</small>
            </div>
        </div>

        <!-- تذييل الفاتورة -->
        <div style="margin: 2rem; padding: 1.5rem; background: #f8f9fa; text-align: center; border-radius: 10px;">
            <p><strong>مجموعة أبراهيم الاحمدي اليمنية للتجارة والخدمات اللوجستية</strong></p>
            <p>شكراً لثقتكم بنا</p>
            <small>تم إنشاء هذا المستند في: <?php echo e(now()->format('Y-m-d H:i:s')); ?></small>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp2\htdocs\app\resources\views/orders/show.blade.php ENDPATH**/ ?>