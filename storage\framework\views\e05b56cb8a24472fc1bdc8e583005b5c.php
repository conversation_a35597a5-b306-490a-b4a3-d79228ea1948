<?php $__env->startSection('title', 'تفاصيل الطلب'); ?>

<?php $__env->startSection('styles'); ?>
<style>
    /* إعدادات الصفحة لورقة A4 */
    @page {
        size: A4 portrait !important;
        margin: 10mm 15mm !important;
    }

    /* تحسينات عامة للطباعة على ورقة A4 */
    @media print {
        html, body {
            width: 210mm !important;
            height: 297mm !important;
            margin: 0 !important;
            padding: 0 !important;
            background: white !important;
            color: black !important;
            -webkit-print-color-adjust: exact !important;
            print-color-adjust: exact !important;
            color-adjust: exact !important;
            overflow: hidden !important;
        }

    /* CSS محسن للطباعة - حل مشكلة المظهر السيء */
    @media print {
        @page {
            size: A4 portrait;
            margin: 15mm;
        }

        * {
            -webkit-print-color-adjust: exact !important;
            color-adjust: exact !important;
            print-color-adjust: exact !important;
            box-sizing: border-box !important;
        }

        html, body {
            width: 100% !important;
            height: auto !important;
            margin: 0 !important;
            padding: 0 !important;
            background: white !important;
            color: black !important;
            font-family: 'Cairo', 'Tahoma', 'Arial', sans-serif !important;
            font-size: 14px !important;
            line-height: 1.5 !important;
            direction: rtl !important;
            text-align: right !important;
        }

        /* إخفاء العناصر غير المرغوبة */
        .action-buttons,
        .no-print,
        .btn,
        .navbar,
        .sidebar,
        footer,
        nav,
        .page-container,
        body::before {
            display: none !important;
        }

        /* إظهار المحتوى الرئيسي */
        .invoice-container {
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
            position: static !important;
            width: 100% !important;
            max-width: none !important;
            margin: 0 !important;
            padding: 0 !important;
            background: white !important;
            color: black !important;
            box-shadow: none !important;
            border: none !important;
            border-radius: 0 !important;
            transform: none !important;
            backdrop-filter: none !important;
        }

        .watermark {
            display: none !important;
        }

        /* تحسين الهيدر */
        .invoice-header {
            display: flex !important;
            visibility: visible !important;
            opacity: 1 !important;
            background: white !important;
            color: black !important;
            padding: 15px !important;
            border-bottom: 3px solid black !important;
            margin-bottom: 20px !important;
            justify-content: space-between !important;
            align-items: center !important;
            page-break-inside: avoid !important;
        }

        .company-info {
            display: flex !important;
            align-items: center !important;
            gap: 15px !important;
        }

        .company-logo {
            width: 60px !important;
            height: 60px !important;
            border: 2px solid black !important;
            border-radius: 0 !important;
            background: white !important;
            padding: 5px !important;
        }

        .company-name {
            font-size: 18px !important;
            font-weight: bold !important;
            color: black !important;
            margin-bottom: 5px !important;
            text-shadow: none !important;
        }

        .company-subtitle {
            font-size: 14px !important;
            color: black !important;
            font-weight: normal !important;
        }

        .invoice-details {
            background: white !important;
            border: 2px solid black !important;
            padding: 10px !important;
            border-radius: 0 !important;
            backdrop-filter: none !important;
            text-align: center !important;
        }

        .invoice-number {
            font-size: 16px !important;
            font-weight: bold !important;
            color: black !important;
            margin-bottom: 5px !important;
            text-shadow: none !important;
        }

        .invoice-date {
            font-size: 14px !important;
            color: black !important;
            font-weight: normal !important;
        }

        /* تحسين الجدول */
        .details-table {
            display: table !important;
            visibility: visible !important;
            opacity: 1 !important;
            width: 100% !important;
            border-collapse: collapse !important;
            margin: 20px 0 !important;
            font-size: 13px !important;
            border: 2px solid black !important;
            page-break-inside: avoid !important;
        }

        .details-table tr {
            display: table-row !important;
            visibility: visible !important;
            opacity: 1 !important;
            page-break-inside: avoid !important;
        }

        .details-table td {
            display: table-cell !important;
            visibility: visible !important;
            opacity: 1 !important;
            border: 1px solid black !important;
            padding: 8px !important;
            background: white !important;
            color: black !important;
            vertical-align: top !important;
            text-align: right !important;
        }

        .label-col {
            background: #f5f5f5 !important;
            font-weight: bold !important;
            width: 25% !important;
        }

        .value-col {
            width: 25% !important;
        }

        .section-title {
            background: #e0e0e0 !important;
            text-align: center !important;
            font-weight: bold !important;
            font-size: 15px !important;
            color: black !important;
            padding: 10px !important;
            border: 2px solid black !important;
        }

        .status-badge {
            background: white !important;
            color: black !important;
            border: 1px solid black !important;
            padding: 5px 10px !important;
            font-size: 12px !important;
            border-radius: 0 !important;
            font-weight: bold !important;
            display: inline-block !important;
        }

        .profit-label {
            background: #d0d0d0 !important;
            font-weight: bold !important;
            color: black !important;
        }

        .profit-value {
            background: #f0f0f0 !important;
            font-weight: bold !important;
            color: black !important;
            font-size: 14px !important;
        }

        .notes-cell {
            padding: 15px !important;
            min-height: 50px !important;
            line-height: 1.5 !important;
            vertical-align: top !important;
            font-size: 13px !important;
            background: white !important;
            border: 1px solid black !important;
        }

        /* تحسين التوقيعات */
        .signature-section {
            display: flex !important;
            visibility: visible !important;
            opacity: 1 !important;
            justify-content: space-between !important;
            margin: 30px 0 !important;
            gap: 20px !important;
            page-break-inside: avoid !important;
            border-top: 2px solid black !important;
            padding-top: 20px !important;
        }

        .signature-box {
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
            text-align: center !important;
            border: 1px solid black !important;
            padding: 15px !important;
            background: white !important;
            width: 30% !important;
        }

        .signature-line {
            border-bottom: 2px solid black !important;
            height: 40px !important;
            margin-bottom: 10px !important;
            background: white !important;
        }

        .signature-label {
            font-size: 14px !important;
            font-weight: bold !important;
            color: black !important;
            margin-bottom: 5px !important;
        }

        .stamp-circle {
            width: 60px !important;
            height: 60px !important;
            border: 2px solid black !important;
            border-radius: 50% !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            margin: 0 auto 10px !important;
            font-size: 12px !important;
            color: black !important;
            background: white !important;
            font-weight: bold !important;
        }

        /* تحسين الفوتر */
        .invoice-footer {
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
            background: #f8f8f8 !important;
            color: black !important;
            border: 2px solid black !important;
            padding: 15px !important;
            text-align: center !important;
            margin-top: 20px !important;
            page-break-inside: avoid !important;
        }

        .footer-content p {
            margin: 5px 0 !important;
            line-height: 1.4 !important;
            font-size: 12px !important;
        }

        .generation-info {
            font-size: 10px !important;
            color: #666 !important;
            margin-top: 10px !important;
            border-top: 1px solid black !important;
            padding-top: 10px !important;
        }

        /* إزالة التأثيرات والحركات */
        * {
            transform: none !important;
            animation: none !important;
            transition: none !important;
            box-shadow: none !important;
        }
    }

    /* CSS إضافي للطباعة */
    body.printing {
        background: white !important;
    }

    body.printing .action-buttons,
    body.printing .no-print,
    body.printing .btn {
        display: none !important;
    }

    body.printing .invoice-container {
        box-shadow: none !important;
        border-radius: 0 !important;
        margin: 0 !important;
        padding: 20px !important;
        background: white !important;
    }



    :root {
        --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        --warning-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        --info-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        --glass-effect: rgba(255, 255, 255, 0.1);
        --shadow-soft: 0 10px 40px rgba(0, 0, 0, 0.1);
        --shadow-medium: 0 15px 50px rgba(0, 0, 0, 0.15);
        --shadow-strong: 0 20px 60px rgba(0, 0, 0, 0.2);
        --border-radius: 20px;
        --border-radius-small: 12px;
        --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    body {
        font-family: 'Cairo', 'Segoe UI', 'Tahoma', 'aealarabiya', 'XB Riyaz', 'DejaVu Sans', 'Arial Unicode MS', sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        background-attachment: fixed;
        direction: rtl;
        text-align: right;
        margin: 0;
        padding: 20px;
        font-size: 14px;
        line-height: 1.6;
        min-height: 100vh;
        position: relative;
    }

    body::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
        pointer-events: none;
        z-index: 1;
    }

    .page-container {
        max-width: 1400px;
        margin: 0 auto;
        position: relative;
        z-index: 2;
    }

    .action-buttons {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        padding: 2rem;
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-medium);
        margin-bottom: 2rem;
        border: 1px solid rgba(255, 255, 255, 0.3);
        position: relative;
        overflow: hidden;
        transition: var(--transition);
    }

    .action-buttons::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.6s;
    }

    .action-buttons:hover::before {
        left: 100%;
    }

    .action-buttons:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-strong);
    }

    .action-buttons h4 {
        color: #2d3748;
        font-weight: 700;
        margin-bottom: 0.5rem;
        font-size: 1.5rem;
        position: relative;
        z-index: 2;
    }

    .action-buttons .text-muted {
        color: #718096 !important;
        font-size: 0.9rem;
        position: relative;
        z-index: 2;
    }

    .btn {
        border-radius: var(--border-radius-small);
        padding: 0.8rem 1.5rem;
        font-weight: 600;
        transition: var(--transition);
        border: none;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.6rem;
        position: relative;
        overflow: hidden;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
        transition: left 0.5s;
    }

    .btn:hover::before {
        left: 100%;
    }

    .btn:hover {
        transform: translateY(-3px) scale(1.02);
        box-shadow: 0 8px 25px rgba(0,0,0,0.2);
    }

    .btn:active {
        transform: translateY(-1px) scale(0.98);
    }

    .btn-secondary {
        background: var(--primary-gradient);
        color: white;
    }

    .btn-warning {
        background: var(--secondary-gradient);
        color: white;
    }

    .btn-success {
        background: var(--success-gradient);
        color: white;
    }

    .btn-info {
        background: var(--info-gradient);
        color: white;
    }

    .btn-primary {
        background: var(--warning-gradient);
        color: white;
    }

    .invoice-container {
        width: 100%;
        max-width: 1000px;
        margin: 0 auto;
        background: rgba(255, 255, 255, 0.98);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-strong);
        padding: 0;
        position: relative;
        overflow: hidden;
        border: 1px solid rgba(255, 255, 255, 0.3);
        transition: var(--transition);
    }

    .invoice-container:hover {
        transform: translateY(-5px);
        box-shadow: 0 30px 80px rgba(0,0,0,0.15);
    }

    .watermark {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%) rotate(-45deg);
        font-size: 150px;
        background: linear-gradient(45deg, rgba(102, 126, 234, 0.02), rgba(240, 147, 251, 0.02));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        font-weight: 900;
        z-index: 1;
        pointer-events: none;
        user-select: none;
        animation: watermarkFloat 6s ease-in-out infinite;
    }

    @keyframes watermarkFloat {
        0%, 100% { transform: translate(-50%, -50%) rotate(-45deg) scale(1); }
        50% { transform: translate(-50%, -50%) rotate(-45deg) scale(1.05); }
    }

    .watermark::before {
        content: "مجموعة أبراهيم الاحمدي";
    }

    .invoice-header {
        background: var(--primary-gradient);
        color: white;
        padding: 2.5rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;
        z-index: 2;
        border-radius: var(--border-radius) var(--border-radius) 0 0;
        overflow: hidden;
    }

    .invoice-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
        animation: headerShine 3s ease-in-out infinite;
    }

    @keyframes headerShine {
        0% { left: -100%; }
        50% { left: 100%; }
        100% { left: -100%; }
    }

    .invoice-header::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="50" height="50" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        opacity: 0.3;
    }

    .company-info {
        display: flex;
        align-items: center;
        gap: 2rem;
        position: relative;
        z-index: 3;
        animation: slideInRight 0.8s ease-out;
    }

    @keyframes slideInRight {
        from {
            opacity: 0;
            transform: translateX(50px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    .company-logo {
        width: 90px;
        height: 90px;
        object-fit: contain;
        border-radius: var(--border-radius-small);
        border: 3px solid rgba(255, 255, 255, 0.4);
        background: rgba(255, 255, 255, 0.15);
        padding: 10px;
        transition: var(--transition);
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    }

    .company-logo:hover {
        transform: scale(1.1) rotate(5deg);
        box-shadow: 0 12px 35px rgba(0,0,0,0.2);
    }

    .company-text {
        text-align: right;
        animation: slideInLeft 0.8s ease-out 0.2s both;
    }

    @keyframes slideInLeft {
        from {
            opacity: 0;
            transform: translateX(-50px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    .company-name {
        font-size: 2rem;
        font-weight: 800;
        margin-bottom: 0.5rem;
        text-shadow: 0 3px 6px rgba(0,0,0,0.2);
        background: linear-gradient(45deg, #ffffff, #f0f8ff);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .company-subtitle {
        font-size: 1.1rem;
        opacity: 0.95;
        font-weight: 500;
        text-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    .invoice-details {
        text-align: left;
        direction: ltr;
        position: relative;
        z-index: 3;
        background: rgba(255, 255, 255, 0.2);
        padding: 1.5rem;
        border-radius: var(--border-radius-small);
        backdrop-filter: blur(15px);
        -webkit-backdrop-filter: blur(15px);
        border: 1px solid rgba(255, 255, 255, 0.3);
        animation: slideInLeft 0.8s ease-out 0.4s both;
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    }

    .invoice-number {
        font-size: 1.4rem;
        font-weight: 800;
        margin-bottom: 0.8rem;
        text-shadow: 0 2px 4px rgba(0,0,0,0.2);
        background: linear-gradient(45deg, #ffffff, #f0f8ff);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .invoice-date {
        font-size: 1rem;
        opacity: 0.9;
        font-weight: 500;
        text-shadow: 0 1px 2px rgba(0,0,0,0.1);
    }

    .details-section {
        margin: 1.5mm 0;
        page-break-inside: avoid;
    }

    .section-title {
        background: #f8f9fa;
        border: 1px solid #ddd;
        padding: 1mm 2mm;
        font-weight: bold;
        font-size: 8px;
        color: #333;
        margin-bottom: 0;
    }

    .details-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        margin: 2.5rem 0;
        font-size: 15px;
        border-radius: var(--border-radius-small);
        overflow: hidden;
        box-shadow: var(--shadow-medium);
        background: white;
        animation: fadeInUp 0.8s ease-out 0.6s both;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .details-table td {
        padding: 1.2rem;
        border: none;
        vertical-align: middle;
        line-height: 1.7;
        border-bottom: 1px solid rgba(226, 232, 240, 0.5);
        transition: var(--transition);
        position: relative;
    }

    .details-table tr:hover td {
        background: rgba(102, 126, 234, 0.02) !important;
        transform: scale(1.01);
    }

    .details-table .label-col {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        font-weight: 700;
        width: 25%;
        color: #2d3748;
        position: relative;
        border-right: 4px solid transparent;
        border-image: var(--primary-gradient) 1;
    }

    .details-table .label-col::before {
        content: '';
        position: absolute;
        right: 0;
        top: 0;
        bottom: 0;
        width: 4px;
        background: var(--primary-gradient);
        border-radius: 0 4px 4px 0;
    }

    .details-table .value-col {
        width: 25%;
        color: #4a5568;
        background: rgba(255, 255, 255, 0.8);
        font-weight: 500;
    }

    .section-title {
        background: var(--primary-gradient) !important;
        text-align: center !important;
        font-weight: 700 !important;
        font-size: 1.2rem !important;
        color: white !important;
        padding: 1.5rem !important;
        text-shadow: 0 2px 4px rgba(0,0,0,0.2);
        position: relative;
        overflow: hidden;
        text-transform: uppercase;
        letter-spacing: 1px;
        border: none !important;
    }

    .section-title::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
        transition: left 0.6s ease;
    }

    .section-title:hover::before {
        left: 100%;
    }

    .section-title::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        width: 0;
        height: 3px;
        background: rgba(255,255,255,0.8);
        transition: all 0.3s ease;
        transform: translateX(-50%);
    }

    .section-title:hover::after {
        width: 80%;
    }

    .status-badge {
        padding: 0.7rem 1.5rem;
        border-radius: 30px;
        font-size: 0.9rem;
        font-weight: 700;
        display: inline-flex;
        align-items: center;
        gap: 0.6rem;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        transition: var(--transition);
        text-transform: uppercase;
        letter-spacing: 0.5px;
        position: relative;
        overflow: hidden;
        border: 2px solid transparent;
    }

    .status-badge::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
        transition: left 0.5s;
    }

    .status-badge:hover::before {
        left: 100%;
    }

    .status-badge:hover {
        transform: translateY(-2px) scale(1.05);
        box-shadow: 0 8px 25px rgba(0,0,0,0.2);
    }

    .status-agreed {
        background: var(--success-gradient);
        color: white;
        border-color: rgba(79, 172, 254, 0.3);
    }

    .status-pending {
        background: var(--warning-gradient);
        color: white;
        border-color: rgba(247, 112, 154, 0.3);
    }

    .status-cancelled {
        background: var(--secondary-gradient);
        color: white;
        border-color: rgba(240, 147, 251, 0.3);
    }



    .profit-label {
        background: linear-gradient(135deg, #48bb78 0%, #38a169 100%) !important;
        font-weight: 600 !important;
        color: white !important;
        position: relative;
    }

    .profit-label::before {
        content: '💰';
        margin-left: 0.5rem;
    }

    .profit-value {
        background: linear-gradient(135deg, #e6fffa 0%, #b2f5ea 100%) !important;
        font-weight: 700 !important;
        color: #065f46 !important;
        font-size: 1.1rem !important;
        text-shadow: 0 1px 2px rgba(0,0,0,0.1);
    }

    .notes-cell {
        padding: 1.5rem !important;
        min-height: 80px !important;
        line-height: 1.6 !important;
        vertical-align: top !important;
        background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%) !important;
        border-radius: 10px !important;
        position: relative;
        font-style: italic;
    }

    .notes-cell::before {
        content: '📝';
        position: absolute;
        top: 1rem;
        right: 1rem;
        font-size: 1.2rem;
        opacity: 0.5;
    }

    /* تحسينات إضافية للتفاعل */
    .details-table tr:hover .value-col {
        background: linear-gradient(135deg, #f0fff4 0%, #e6fffa 100%);
        transition: all 0.3s ease;
    }

    .invoice-container:hover .watermark {
        opacity: 0.08;
        transition: opacity 0.5s ease;
    }

    /* تأثيرات الحركة */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .details-table {
        animation: fadeInUp 0.6s ease-out;
    }

    .signature-section {
        animation: fadeInUp 0.8s ease-out;
    }

    .invoice-footer {
        animation: fadeInUp 1s ease-out;
    }

    /* تحسينات خاصة للطباعة الرسمية */
    .print-mode {
        background: white !important;
    }

    .print-mode .invoice-container {
        box-shadow: none !important;
        border-radius: 0 !important;
        margin: 0 !important;
        padding: 0 !important;
    }

    .print-mode .watermark {
        display: none !important;
    }

    .print-mode .invoice-header {
        border-radius: 0 !important;
    }

    .print-mode .invoice-footer {
        border-radius: 0 !important;
    }

    .invoice-footer {
        margin: 3rem 2rem 2rem;
        padding: 2rem;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        text-align: center;
        border-radius: 0 0 20px 20px;
        position: relative;
        overflow: hidden;
    }

    .invoice-footer::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        opacity: 0.1;
    }

    .footer-content {
        position: relative;
        z-index: 2;
    }

    .footer-content p {
        margin: 0.5rem 0;
        font-size: 1rem;
        line-height: 1.6;
    }

    .footer-content strong {
        font-weight: 700;
        font-size: 1.1rem;
    }

    .generation-info {
        font-size: 0.8rem;
        opacity: 0.8;
        margin-top: 1rem;
        border-top: 1px solid rgba(255, 255, 255, 0.2);
        padding-top: 1rem;
    }

    .invoice-footer {
        margin-top: 30px;
        padding-top: 20px;
        border-top: 2px solid #e9ecef;
        text-align: center;
        font-size: 12px;
        color: #6c757d;
        background: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
    }

    .footer-content p {
        margin: 5px 0;
    }

    .generation-info {
        font-size: 10px;
        color: #adb5bd;
        margin-top: 10px;
        border-top: 1px solid #dee2e6;
        padding-top: 10px;
    }

    .signature-section {
        display: flex;
        justify-content: space-between;
        margin: 3rem 2rem 2rem;
        page-break-inside: avoid;
        gap: 2rem;
    }

    .signature-box {
        flex: 1;
        text-align: center;
        padding: 2rem 1rem;
        background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
    }

    .signature-box:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
    }

    .signature-line {
        border-bottom: 2px dashed #cbd5e0;
        height: 60px;
        margin-bottom: 1rem;
        position: relative;
    }

    .signature-line::before {
        content: '✍️';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 1.5rem;
        opacity: 0.3;
    }

    .signature-label {
        font-size: 1rem;
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 0.5rem;
    }

    .stamp-area {
        text-align: center;
        width: 20mm;
    }

    .stamp-circle {
        width: 80px;
        height: 80px;
        border: 3px dashed #667eea;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
        font-size: 0.9rem;
        color: #667eea;
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
        position: relative;
        transition: all 0.3s ease;
    }

    .stamp-circle:hover {
        transform: rotate(5deg);
        border-color: #764ba2;
        color: #764ba2;
    }

    .stamp-circle::before {
        content: '🏢';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 2rem;
        opacity: 0.3;
    }

    .footer-info {
        position: fixed;
        bottom: 10px;
        width: 100%;
        text-align: center;
        font-size: 11px;
        color: #888;
        border-top: 1px solid #ddd;
        padding-top: 10px;
        page-break-inside: avoid;
    }

    .watermark {
        position: absolute;
        z-index: 0;
        width: 100%;
        height: 100%;
        opacity: 0.05;
        top: 0;
        left: 0;
        background-image: url('<?php echo e(asset('images/logos/aswsd.png')); ?>');
        background-position: center;
        background-size: contain;
        background-repeat: no-repeat;
    }





    @media screen and (max-width: 768px) {
        .a4-container {
            width: 100%;
            margin: 10px;
            padding: 15px;
        }

        .invoice-header {
            flex-direction: column;
            text-align: center;
        }

        .company-info {
            justify-content: center;
            gap: 5px;
        }

        .company-logo {
            width: 35px;
            height: 35px;
        }

        .invoice-details {
            text-align: center;
            direction: rtl;
            margin-top: 10px;
        }

        .details-table .label-col,
        .details-table .value-col {
            width: 50%;
        }

        .signature-section {
            flex-direction: column;
            gap: 20px;
        }
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="page-container">
    <!-- Action Buttons -->
    <div class="action-buttons">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h4 class="mb-0">تفاصيل الطلب: <?php echo e($order->branch_serial ?? $order->order_number ?? '#' . $order->id); ?></h4>
                <small class="text-muted">تم الإنشاء في: <?php echo e($order->created_at->format('Y-m-d H:i')); ?></small>
            </div>
            <div class="d-flex gap-2">
                <a href="<?php echo e(route('orders.index')); ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-1"></i> العودة للقائمة
                </a>
                <a href="<?php echo e(route('orders.edit', $order)); ?>" class="btn btn-warning">
                    <i class="fas fa-edit me-1"></i> تعديل
                </a>
                <button class="btn btn-primary print-btn" onclick="printOrder()">
                    <i class="fas fa-print me-1"></i> طباعة الطلب
                </button>
            </div>
        </div>
    </div>

    <!-- Document Container -->
    <div class="invoice-container print-content">
        <!-- الشعار المائي -->
        <div class="watermark no-print"></div>

        <!-- Invoice Header -->
        <div class="invoice-header print-content">
            <div class="company-info">
                <img src="<?php echo e(asset('images/logos/aswsd.png')); ?>" alt="شعار الشركة" class="company-logo">
                <div class="company-text">
                    <div class="company-name">مجموعة أبراهيم الاحمدي اليمنية</div>
                    <div class="company-subtitle">للتجارة والخدمات اللوجستية</div>
                </div>
            </div>
            <div class="invoice-details">
                <div class="invoice-number">طلب #<?php echo e($order->branch_serial ?? $order->order_number ?? $order->id); ?></div>
                <div class="invoice-date"><?php echo e($order->created_at->format('Y/m/d')); ?></div>
            </div>
        </div>

        <!-- جدول تفاصيل الطلب الموحد -->
        <table class="details-table print-content">
        <tr>
            <td class="label-col">رقم الطلب</td>
            <td class="value-col"><?php echo e($order->branch_serial ?? $order->order_number ?? $order->id); ?></td>
            <td class="label-col">التاريخ</td>
            <td class="value-col"><?php echo e($order->request_date ?? $order->created_at->format('Y-m-d')); ?></td>
        </tr>
        <tr>
            <td class="label-col">اسم العميل</td>
            <td class="value-col"><?php echo e($order->customer_name ?? 'غير محدد'); ?></td>
            <td class="label-col">رقم الهاتف</td>
            <td class="value-col"><?php echo e($order->phone_number ?? 'غير محدد'); ?></td>
        </tr>
        <tr>
            <td class="label-col">اسم المستلم</td>
            <td class="value-col"><?php echo e($order->recipient_name ?? 'غير محدد'); ?></td>
            <td class="label-col">نوع الخدمة</td>
            <td class="value-col"><?php echo e($order->service_type ?? 'غير محدد'); ?></td>
        </tr>
        <tr>
            <td class="label-col">حالة الطلب</td>
            <td class="value-col">
                <?php switch($order->status):
                    case ('تم الاتفاق'): ?>
                        <span class="status-badge status-agreed">✓ تم الاتفاق</span>
                        <?php break; ?>
                    <?php case ('قيد المتابعة'): ?>
                        <span class="status-badge status-pending">⏳ قيد المتابعة</span>
                        <?php break; ?>
                    <?php case ('ملغي'): ?>
                        <span class="status-badge status-cancelled">✗ ملغي</span>
                        <?php break; ?>
                    <?php default: ?>
                        <span class="status-badge"><?php echo e($order->status ?? 'غير محدد'); ?></span>
                <?php endswitch; ?>
            </td>
            <td class="label-col">المسؤول</td>
            <td class="value-col"><?php echo e($order->user_name ?? ($order->user->name ?? 'غير محدد')); ?></td>
        </tr>

        <!-- تفاصيل البضاعة -->
        <tr>
            <td colspan="4" class="section-title">تفاصيل البضاعة</td>
        </tr>
        <tr>
            <td class="label-col">نوع البضاعة</td>
            <td class="value-col"><?php echo e($order->goods_name ?? 'غير محدد'); ?></td>
            <td class="label-col">بلد المنشأ</td>
            <td class="value-col"><?php echo e($order->country_of_origin ?? 'غير محدد'); ?></td>
        </tr>
        <tr>
            <td class="label-col">الوزن</td>
            <td class="value-col"><?php echo e($order->weight ?? 'غير محدد'); ?></td>
            <td class="label-col">الكمية</td>
            <td class="value-col"><?php echo e($order->quantity ?? 'غير محدد'); ?></td>
        </tr>
        <tr>
            <td class="label-col">منطقة المغادرة</td>
            <td class="value-col"><?php echo e($order->departure_area ?? 'غير محدد'); ?></td>
            <td class="label-col">منطقة التسليم</td>
            <td class="value-col"><?php echo e($order->delivery_area ?? 'غير محدد'); ?></td>
        </tr>

        <!-- التفاصيل المالية -->
        <?php
            $currencyDisplay = match($order->currency ?? 'ريال') {
                'ريال' => 'ريال يمني',
                'دولار' => 'دولار أمريكي',
                'ريال سعودي' => 'ريال سعودي',
                'درهم' => 'درهم إماراتي',
                default => $order->currency ?? 'ريال يمني'
            };
        ?>
        <tr>
            <td colspan="4" class="section-title">التفاصيل المالية</td>
        </tr>
        <tr>
            <td class="label-col">رسوم الخدمة</td>
            <td class="value-col"><?php echo e($order->service_fees ? number_format($order->service_fees, 2) . ' ' . $currencyDisplay : 'غير محدد'); ?></td>
            <td class="label-col">المبلغ المدفوع</td>
            <td class="value-col"><?php echo e($order->paid_amount ? number_format($order->paid_amount, 2) . ' ' . $currencyDisplay : 'غير محدد'); ?></td>
        </tr>
        <tr>
            <td class="label-col">مبلغ العميل المتفق عليه</td>
            <td class="value-col"><?php echo e($order->customer_agreed_amount ? number_format($order->customer_agreed_amount, 2) . ' ' . $currencyDisplay : 'غير محدد'); ?></td>
            <td class="label-col">المبلغ المتبقي</td>
            <td class="value-col"><?php echo e($order->remaining_amount ? number_format($order->remaining_amount, 2) . ' ' . $currencyDisplay : 'غير محدد'); ?></td>
        </tr>
        <tr>
            <td class="label-col">مبلغ الوكيل المتفق عليه</td>
            <td class="value-col"><?php echo e($order->agent_agreed_amount ? number_format($order->agent_agreed_amount, 2) . ' ' . $currencyDisplay : 'غير محدد'); ?></td>
            <td class="label-col">مصاريف أخرى</td>
            <td class="value-col"><?php echo e($order->other_expenses ? number_format($order->other_expenses, 2) . ' ' . $currencyDisplay : 'غير محدد'); ?></td>
        </tr>
        <tr>
            <td class="label-col">العملة</td>
            <td class="value-col"><?php echo e($currencyDisplay); ?></td>
            <td class="label-col profit-label">صافي الربح</td>
            <td class="value-col profit-value"><?php echo e($order->profit ? number_format($order->profit, 2) . ' ' . $currencyDisplay : 'غير محدد'); ?></td>
        </tr>

        <!-- ملاحظات -->
        <?php if($order->notes): ?>
        <tr>
            <td colspan="4" class="section-title">ملاحظات</td>
        </tr>
        <tr>
            <td colspan="4" class="notes-cell">
                <?php echo e($order->notes); ?>

            </td>
        </tr>
        <?php endif; ?>
    </table>

    <!-- التوقيعات -->
    <div class="signature-section">
        <div class="signature-box">
            <div class="signature-line"></div>
            <div class="signature-label">توقيع المدير</div>
            <small><?php echo e($order->user_name ?? ($order->user->name ?? 'غير محدد')); ?></small>
        </div>
        <div class="signature-box">
            <div class="stamp-circle">ختم الشركة</div>
            <div class="signature-label">ختم المؤسسة</div>
        </div>
        <div class="signature-box">
            <div class="signature-line"></div>
            <div class="signature-label">توقيع العميل</div>
            <small><?php echo e($order->customer_name ?? 'غير محدد'); ?></small>
        </div>
    </div>

    <!-- معلومات الشركة في الأسفل -->
    <div class="invoice-footer">
        <div class="footer-content">
            <p><strong>مجموعة أبراهيم الاحمدي اليمنية للتجارة والخدمات اللوجستية</strong></p>
            <p>الهاتف: 738504800 | البريد الإلكتروني: <EMAIL></p>
            <p>العنوان: اليمن - حضرموت - المكلا</p>
            <p class="generation-info">تم توليد هذا المستند بتاريخ: <?php echo e(now()->format('Y-m-d H:i:s')); ?> | رقم الطلب: <?php echo e($order->branch_serial ?? $order->order_number ?? '#' . $order->id); ?></p>
        </div>
    </div>
    </div> <!-- نهاية invoice-container -->
</div> <!-- نهاية page-container -->

<?php $__env->startPush('scripts'); ?>
<script src="<?php echo e(asset('js/print-optimization.js')); ?>"></script>
<script>
// دالة طباعة محسنة - حل مشكلة عدم ظهور محتوى الطباعة
function optimizedPrint() {
    console.log('بدء عملية الطباعة المحسنة...');

    try {
        // تحديد عنوان المستند للطباعة
        const originalTitle = document.title;
        const orderNumber = '<?php echo e($order->branch_serial ?? $order->order_number ?? "#" . $order->id); ?>';
        document.title = `طلب-${orderNumber} - مجموعة أبراهيم الاحمدي`;

        // إضافة كلاس للطباعة
        document.body.classList.add('print-mode');
        document.documentElement.classList.add('print-ready');

        // إخفاء العناصر غير المرغوبة
        const elementsToHide = document.querySelectorAll(
            '.action-buttons, .no-print, .navbar, .sidebar, footer, nav, .pagination, .breadcrumb, .btn:not(.print-btn), header'
        );

        console.log(`إخفاء ${elementsToHide.length} عنصر غير مرغوب`);

        // حفظ الحالة الأصلية للعناصر
        const originalDisplayValues = [];
        elementsToHide.forEach((el, index) => {
            originalDisplayValues[index] = el.style.display;
            el.style.display = 'none';
        });

        // إظهار المحتوى الرئيسي بشكل صريح
        const contentSelectors = [
            '.invoice-container',
            '.page-container',
            '.details-table',
            '.print-content',
            '.invoice-header',
            '.invoice-body',
            '.invoice-footer',
            '.signature-section',
            '.company-info',
            '.company-text',
            '.invoice-details'
        ];

        contentSelectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            console.log(`إظهار ${elements.length} عنصر من نوع ${selector}`);

            elements.forEach(element => {
                element.style.display = 'block';
                element.style.visibility = 'visible';
                element.style.opacity = '1';
                element.style.position = 'static';
                element.style.overflow = 'visible';
                element.style.zIndex = '9999';
                element.setAttribute('data-print-visible', 'true');
            });
        });

        // تحسين الجداول للطباعة
        const tables = document.querySelectorAll('table.details-table');
        console.log(`تحسين ${tables.length} جدول للطباعة`);

        tables.forEach(table => {
            table.style.display = 'table';
            table.style.visibility = 'visible';
            table.style.opacity = '1';
            table.style.width = '100%';
            table.style.maxWidth = '190mm';
            table.style.borderCollapse = 'collapse';
            table.style.border = '1px solid #000';
            table.style.pageBreakInside = 'avoid';
            table.style.margin = '10mm 0';

            // تحسين صفوف الجدول
            const rows = table.querySelectorAll('tr');
            rows.forEach(row => {
                row.style.display = 'table-row';
                row.style.visibility = 'visible';
                row.style.pageBreakInside = 'avoid';
            });

            // تحسين خلايا الجدول
            const cells = table.querySelectorAll('td, th');
            cells.forEach(cell => {
                cell.style.display = 'table-cell';
                cell.style.visibility = 'visible';
                cell.style.border = '1px solid #000';
                cell.style.padding = '3mm';
            });
        });

        // تحسين الصور
        const images = document.querySelectorAll('.company-logo, img');
        console.log(`تحسين ${images.length} صورة للطباعة`);

        images.forEach(img => {
            img.style.display = 'inline-block';
            img.style.visibility = 'visible';
            img.style.opacity = '1';
            img.style.maxWidth = '100%';
            img.style.height = 'auto';
        });

        // إضافة ورقة أنماط مؤقتة للطباعة - تحسين لورقة A4
        const printStyle = document.createElement('style');
        printStyle.id = 'temp-print-styles';
        printStyle.textContent = `
            @media print {
                @page {
                    size: A4 portrait !important;
                    margin: 10mm 15mm !important;
                }

                html, body {
                    width: 210mm !important;
                    height: 297mm !important;
                    margin: 0 !important;
                    padding: 0 !important;
                    background: white !important;
                    color: black !important;
                    -webkit-print-color-adjust: exact !important;
                    print-color-adjust: exact !important;
                    color-adjust: exact !important;
                }

                body * {
                    visibility: hidden !important;
                }

                .print-content, .invoice-container, .page-container, .details-table,
                .invoice-header, .invoice-footer, .signature-section, .company-info,
                .company-text, .invoice-details, .company-logo, .footer-content,
                .print-content *, .invoice-container *, .page-container *, .details-table *,
                .invoice-header *, .invoice-footer *, .signature-section *, .company-info *,
                .company-text *, .invoice-details *, .footer-content * {
                    visibility: visible !important;
                    display: block !important;
                    position: static !important;
                    overflow: visible !important;
                    background: white !important;
                    color: black !important;
                    opacity: 1 !important;
                }

                .invoice-container {
                    width: 190mm !important;
                    max-width: 190mm !important;
                    margin: 0 auto !important;
                    padding: 10mm !important;
                    box-shadow: none !important;
                    border: 1px solid #ddd !important;
                }

                table.details-table {
                    display: table !important;
                    width: 100% !important;
                    max-width: 190mm !important;
                    border-collapse: collapse !important;
                    page-break-inside: avoid !important;
                    margin: 10mm 0 !important;
                }

                table.details-table tr {
                    display: table-row !important;
                    page-break-inside: avoid !important;
                }

                table.details-table td, table.details-table th {
                    display: table-cell !important;
                    border: 1px solid #000 !important;
                    padding: 3mm !important;
                }

                .signature-section {
                    display: flex !important;
                    justify-content: space-between !important;
                    margin-top: 15mm !important;
                    page-break-inside: avoid !important;
                }

                .signature-box {
                    display: inline-block !important;
                    text-align: center !important;
                    width: 30% !important;
                }

                .signature-line {
                    border-bottom: 1px solid #000 !important;
                    height: 10mm !important;
                    margin-bottom: 3mm !important;
                }

                .stamp-circle {
                    border: 1px dashed #000 !important;
                    border-radius: 50% !important;
                    width: 20mm !important;
                    height: 20mm !important;
                    display: flex !important;
                    align-items: center !important;
                    justify-content: center !important;
                    margin: 0 auto 3mm !important;
                }

                .invoice-footer {
                    margin-top: 15mm !important;
                    text-align: center !important;
                    font-size: 10pt !important;
                    border-top: 1px solid #ddd !important;
                    padding-top: 5mm !important;
                    page-break-inside: avoid !important;
                }

                .company-logo {
                    display: inline-block !important;
                    max-width: 50mm !important;
                    height: auto !important;
                }

                .no-print {
                    display: none !important;
                }
            }
        `;
        document.head.appendChild(printStyle);

        // إعداد معالج ما بعد الطباعة
        window.onafterprint = () => {
            console.log('انتهاء الطباعة المحسنة...');

            try {
                // استعادة العناصر المخفية
                elementsToHide.forEach((el, index) => {
                    el.style.display = originalDisplayValues[index];
                });

                // إزالة كلاس الطباعة
                document.body.classList.remove('print-mode');
                document.documentElement.classList.remove('print-ready');

                // استعادة العنوان الأصلي
                document.title = originalTitle;

                // إزالة ورقة الأنماط المؤقتة
                const tempStyle = document.getElementById('temp-print-styles');
                if (tempStyle) {
                    tempStyle.remove();
                }

                // إزالة السمات المضافة
                document.querySelectorAll('[data-print-visible="true"]').forEach(el => {
                    el.removeAttribute('data-print-visible');
                    el.style.display = '';
                    el.style.visibility = '';
                    el.style.opacity = '';
                    el.style.position = '';
                    el.style.overflow = '';
                    el.style.zIndex = '';
                });

                // إزالة معالج الحدث
                window.onafterprint = null;
            } catch (error) {
                console.error('خطأ أثناء استعادة العناصر بعد الطباعة:', error);
            }
        };

        // استخدام setTimeout للتأكد من تطبيق التغييرات قبل الطباعة
        setTimeout(() => {
            console.log('تنفيذ أمر الطباعة...');
            window.print();
        }, 500);
    } catch (error) {
        console.error('خطأ أثناء تحضير الطباعة:', error);
        alert('حدث خطأ أثناء تحضير الطباعة. يرجى المحاولة مرة أخرى.');
    }
}

// طباعة سريعة محسنة
function quickOptimizedPrint() {
    console.log('بدء الطباعة السريعة المحسنة...');

    // إظهار المحتوى الرئيسي فقط
    const contentSelectors = ['.invoice-container', '.page-container', '.details-table', '.print-content'];
    contentSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
            element.style.display = 'block';
            element.style.visibility = 'visible';
            element.style.opacity = '1';
        });
    });

    // تطبيق تحسينات سريعة
    document.body.style.fontSize = '10px';
    document.body.style.lineHeight = '1.2';
    document.body.style.background = 'white';
    document.body.style.color = 'black';

    // إضافة ورقة أنماط مؤقتة للطباعة السريعة - تحسين لورقة A4
    const quickStyle = document.createElement('style');
    quickStyle.id = 'quick-print-style';
    quickStyle.textContent = `
        @media print {
            @page {
                size: A4 portrait !important;
                margin: 10mm !important;
            }

            html, body {
                width: 210mm !important;
                height: 297mm !important;
                margin: 0 !important;
                padding: 0 !important;
                background: white !important;
                color: black !important;
                overflow: hidden !important;
            }

            body * { visibility: hidden; }

            .invoice-container, .invoice-container *, .page-container, .page-container *, .details-table, .details-table *, .print-content, .print-content * {
                visibility: visible !important;
                display: block !important;
            }

            table {
                display: table !important;
                width: 100% !important;
                max-width: 190mm !important;
                border-collapse: collapse !important;
            }

            tr { display: table-row !important; }

            td, th {
                display: table-cell !important;
                border: 1px solid #000 !important;
                padding: 2mm !important;
            }
        }
    `;
    document.head.appendChild(quickStyle);

    // طباعة فورية
    setTimeout(() => {
        window.print();

        // استعادة الإعدادات
        setTimeout(() => {
            document.body.style.fontSize = '';
            document.body.style.lineHeight = '';
            document.body.style.background = '';
            document.body.style.color = '';

            // إزالة ورقة الأنماط المؤقتة
            const style = document.getElementById('quick-print-style');
            if (style) {
                style.remove();
            }
        }, 1000);
    }, 100);
}

// طباعة محسنة للفواتير - تحسين لورقة A4
function printInvoice() {
    console.log('بدء طباعة الفاتورة المحسنة على ورقة A4...');

    try {
        // تحديد عنوان المستند للطباعة
        const originalTitle = document.title;
        const orderNumber = '<?php echo e($order->branch_serial ?? $order->order_number ?? "#" . $order->id); ?>';
        document.title = `فاتورة-${orderNumber} - مجموعة أبراهيم الاحمدي`;

        // إضافة كلاس للطباعة
        document.body.classList.add('invoice-print-mode');
        document.documentElement.classList.add('invoice-print-ready');

        // إظهار محتوى الفاتورة فقط
        const invoiceContainer = document.querySelector('.invoice-container');
        if (!invoiceContainer) {
            console.error('لم يتم العثور على حاوية الفاتورة!');
            alert('لم يتم العثور على حاوية الفاتورة!');
            return;
        }

        console.log('تم العثور على حاوية الفاتورة، جاري تحضير الطباعة...');

        // إخفاء جميع العناصر غير المرغوبة
        const elementsToHide = document.querySelectorAll(
            '.action-buttons, .no-print, .navbar, .sidebar, footer, nav, .pagination, .breadcrumb, .btn, header, #sidebar, #navbar'
        );

        console.log(`إخفاء ${elementsToHide.length} عنصر غير مرغوب`);

        // حفظ الحالة الأصلية للعناصر
        const originalStyles = new Map();
        elementsToHide.forEach(el => {
            originalStyles.set(el, {
                display: el.style.display,
                visibility: el.style.visibility
            });
            el.style.display = 'none';
            el.style.visibility = 'hidden';
        });

        // تحسين حاوية الفاتورة
        const originalInvoiceStyles = {
            position: invoiceContainer.style.position,
            display: invoiceContainer.style.display,
            visibility: invoiceContainer.style.visibility,
            opacity: invoiceContainer.style.opacity,
            width: invoiceContainer.style.width,
            maxWidth: invoiceContainer.style.maxWidth,
            margin: invoiceContainer.style.margin,
            padding: invoiceContainer.style.padding,
            background: invoiceContainer.style.background,
            zIndex: invoiceContainer.style.zIndex,
            boxShadow: invoiceContainer.style.boxShadow,
            border: invoiceContainer.style.border
        };

        // تطبيق أنماط الطباعة على حاوية الفاتورة
        invoiceContainer.style.display = 'block';
        invoiceContainer.style.visibility = 'visible';
        invoiceContainer.style.opacity = '1';
        invoiceContainer.style.position = 'relative';
        invoiceContainer.style.width = '210mm';
        invoiceContainer.style.maxWidth = '210mm';
        invoiceContainer.style.margin = '0 auto';
        invoiceContainer.style.padding = '10mm';
        invoiceContainer.style.background = 'white';
        invoiceContainer.style.zIndex = '9999';
        invoiceContainer.style.boxShadow = 'none';
        invoiceContainer.style.border = '1px solid #ddd';

        // تحسين الجداول
        const tables = invoiceContainer.querySelectorAll('table');
        tables.forEach(table => {
            table.style.width = '100%';
            table.style.maxWidth = '190mm';
            table.style.borderCollapse = 'collapse';
            table.style.pageBreakInside = 'avoid';
            table.style.border = '1px solid #000';

            const rows = table.querySelectorAll('tr');
            rows.forEach(row => {
                row.style.pageBreakInside = 'avoid';
            });

            const cells = table.querySelectorAll('td, th');
            cells.forEach(cell => {
                cell.style.border = '1px solid #000';
                cell.style.padding = '3mm';
            });
        });

        // تحسين الصور
        const images = invoiceContainer.querySelectorAll('img');
        images.forEach(img => {
            img.style.maxWidth = '100%';
            img.style.height = 'auto';
        });

        // إضافة ورقة أنماط مؤقتة للطباعة
        const invoiceStyle = document.createElement('style');
        invoiceStyle.id = 'invoice-print-style';
        invoiceStyle.textContent = `
            @media print {
                @page {
                    size: A4 portrait !important;
                    margin: 10mm !important;
                }

                html, body {
                    width: 210mm !important;
                    height: 297mm !important;
                    margin: 0 !important;
                    padding: 0 !important;
                    background: white !important;
                    color: black !important;
                    -webkit-print-color-adjust: exact !important;
                    print-color-adjust: exact !important;
                    color-adjust: exact !important;
                }

                body * {
                    visibility: hidden !important;
                }

                .invoice-container, .invoice-container * {
                    visibility: visible !important;
                    opacity: 1 !important;
                }

                .invoice-container {
                    position: absolute !important;
                    left: 0 !important;
                    top: 0 !important;
                    width: 190mm !important;
                    max-width: 190mm !important;
                    margin: 0 auto !important;
                    padding: 10mm !important;
                    box-shadow: none !important;
                    border: none !important;
                    background: white !important;
                    display: block !important;
                }

                .invoice-header {
                    display: flex !important;
                    justify-content: space-between !important;
                    align-items: center !important;
                    margin-bottom: 10mm !important;
                    padding-bottom: 5mm !important;
                    border-bottom: 1px solid #ddd !important;
                }

                .company-info {
                    display: flex !important;
                    align-items: center !important;
                }

                .company-logo {
                    display: inline-block !important;
                    max-width: 30mm !important;
                    height: auto !important;
                    margin-left: 5mm !important;
                }

                .company-text {
                    display: inline-block !important;
                }

                .company-name {
                    font-size: 16pt !important;
                    font-weight: bold !important;
                }

                .company-subtitle {
                    font-size: 12pt !important;
                }

                .invoice-details {
                    text-align: left !important;
                }

                .invoice-number, .invoice-date {
                    font-size: 12pt !important;
                    margin-bottom: 2mm !important;
                }

                table.details-table {
                    display: table !important;
                    width: 100% !important;
                    max-width: 190mm !important;
                    border-collapse: collapse !important;
                    margin: 10mm 0 !important;
                    page-break-inside: avoid !important;
                }

                table.details-table tr {
                    display: table-row !important;
                    page-break-inside: avoid !important;
                }

                table.details-table td, table.details-table th {
                    display: table-cell !important;
                    border: 1px solid #000 !important;
                    padding: 3mm !important;
                    font-size: 10pt !important;
                }

                .signature-section {
                    display: flex !important;
                    justify-content: space-between !important;
                    margin-top: 15mm !important;
                    page-break-inside: avoid !important;
                }

                .signature-box {
                    display: inline-block !important;
                    text-align: center !important;
                    width: 30% !important;
                }

                .signature-line {
                    border-bottom: 1px solid #000 !important;
                    height: 10mm !important;
                    margin-bottom: 3mm !important;
                }

                .stamp-circle {
                    border: 1px dashed #000 !important;
                    border-radius: 50% !important;
                    width: 20mm !important;
                    height: 20mm !important;
                    display: flex !important;
                    align-items: center !important;
                    justify-content: center !important;
                    margin: 0 auto 3mm !important;
                }

                .invoice-footer {
                    margin-top: 15mm !important;
                    text-align: center !important;
                    font-size: 9pt !important;
                    border-top: 1px solid #ddd !important;
                    padding-top: 5mm !important;
                    page-break-inside: avoid !important;
                }

                .no-print {
                    display: none !important;
                }

                .watermark {
                    display: none !important;
                }
            }
        `;
        document.head.appendChild(invoiceStyle);

        // إعداد معالج ما بعد الطباعة
        window.onafterprint = () => {
            console.log('انتهاء طباعة الفاتورة...');

            try {
                // إزالة كلاس الطباعة
                document.body.classList.remove('invoice-print-mode');
                document.documentElement.classList.remove('invoice-print-ready');

                // استعادة العنوان الأصلي
                document.title = originalTitle;

                // إزالة ورقة الأنماط المؤقتة
                const style = document.getElementById('invoice-print-style');
                if (style) {
                    style.remove();
                }

                // استعادة العناصر المخفية
                elementsToHide.forEach(el => {
                    const originalStyle = originalStyles.get(el);
                    if (originalStyle) {
                        el.style.display = originalStyle.display;
                        el.style.visibility = originalStyle.visibility;
                    }
                });

                // استعادة خصائص الفاتورة
                if (invoiceContainer) {
                    invoiceContainer.style.position = originalInvoiceStyles.position;
                    invoiceContainer.style.display = originalInvoiceStyles.display;
                    invoiceContainer.style.visibility = originalInvoiceStyles.visibility;
                    invoiceContainer.style.opacity = originalInvoiceStyles.opacity;
                    invoiceContainer.style.width = originalInvoiceStyles.width;
                    invoiceContainer.style.maxWidth = originalInvoiceStyles.maxWidth;
                    invoiceContainer.style.margin = originalInvoiceStyles.margin;
                    invoiceContainer.style.padding = originalInvoiceStyles.padding;
                    invoiceContainer.style.background = originalInvoiceStyles.background;
                    invoiceContainer.style.zIndex = originalInvoiceStyles.zIndex;
                    invoiceContainer.style.boxShadow = originalInvoiceStyles.boxShadow;
                    invoiceContainer.style.border = originalInvoiceStyles.border;
                }

                // إزالة معالج الحدث
                window.onafterprint = null;
            } catch (error) {
                console.error('خطأ أثناء استعادة العناصر بعد طباعة الفاتورة:', error);
            }
        };

        // استخدام setTimeout للتأكد من تطبيق التغييرات قبل الطباعة
        setTimeout(() => {
            console.log('تنفيذ أمر طباعة الفاتورة...');
            window.print();
        }, 500);
    } catch (error) {
        console.error('خطأ أثناء تحضير طباعة الفاتورة:', error);
        alert('حدث خطأ أثناء تحضير طباعة الفاتورة. يرجى المحاولة مرة أخرى.');
    }
}

// تهيئة الطباعة وإضافة التحسينات
document.addEventListener('DOMContentLoaded', function() {
    console.log('تهيئة صفحة تفاصيل الطلب...');

    try {
        // تحسين أزرار الطباعة
        const printButtons = document.querySelectorAll('[onclick*="print"], .print-btn, button:has(i.fa-print)');
        console.log(`تم العثور على ${printButtons.length} زر طباعة`);

        printButtons.forEach(btn => {
            // إضافة كلاس للتحكم في الطباعة
            btn.classList.add('print-optimized-btn');

            // التأكد من أن الزر يستخدم الدالة المناسبة
            if (btn.textContent.includes('فاتورة')) {
                btn.onclick = printInvoice;
            } else if (btn.textContent.includes('سريعة')) {
                btn.onclick = quickOptimizedPrint;
            } else {
                btn.onclick = optimizedPrint;
            }

            // إضافة تأثير بصري عند التحويم
            btn.addEventListener('mouseover', function() {
                this.style.transform = 'translateY(-2px)';
                this.style.boxShadow = '0 4px 8px rgba(0,0,0,0.2)';
            });

            btn.addEventListener('mouseout', function() {
                this.style.transform = '';
                this.style.boxShadow = '';
            });
        });

        // إضافة معلومات إضافية للطباعة
        const invoiceContainer = document.querySelector('.invoice-container');
        if (invoiceContainer) {
            console.log('تحسين حاوية الفاتورة للطباعة...');

            // إضافة معرف فريد للطباعة
            invoiceContainer.setAttribute('data-print-id', 'order-<?php echo e($order->id); ?>-<?php echo e(now()->timestamp); ?>');

            // إضافة كلاس للطباعة
            invoiceContainer.classList.add('print-ready');

            // تحسين جودة الصور للطباعة
            const images = invoiceContainer.querySelectorAll('img');
            images.forEach(img => {
                img.style.imageRendering = 'crisp-edges';
                img.style.imageRendering = '-webkit-optimize-contrast';
                img.setAttribute('data-print-optimized', 'true');
            });

            // التأكد من أن جميع العناصر الداخلية مرئية للطباعة
            const innerElements = invoiceContainer.querySelectorAll('*');
            innerElements.forEach(el => {
                if (el.classList.contains('no-print')) {
                    return;
                }
                el.classList.add('print-content');
            });
        }

        // تحسين الجداول للطباعة
        const tables = document.querySelectorAll('table.details-table');
        if (tables.length > 0) {
            console.log(`تحسين ${tables.length} جدول للطباعة...`);

            tables.forEach(table => {
                table.classList.add('print-ready');
                table.setAttribute('data-print-optimized', 'true');
            });
        }

        // تحسين الخطوط للطباعة
        const printFontStyle = document.createElement('style');
        printFontStyle.id = 'print-font-optimization';
        printFontStyle.textContent = `
            @media print {
                * {
                    -webkit-font-smoothing: antialiased !important;
                    -moz-osx-font-smoothing: grayscale !important;
                    text-rendering: optimizeLegibility !important;
                    font-family: 'Cairo', 'Segoe UI', 'Tahoma', 'Arial', sans-serif !important;
                }

                @page {
                    size: A4 portrait !important;
                    margin: 10mm 15mm !important;
                }

                html, body {
                    width: 210mm !important;
                    height: 297mm !important;
                    margin: 0 !important;
                    padding: 0 !important;
                }

                .print-content, .print-ready {
                    display: block !important;
                    visibility: visible !important;
                    opacity: 1 !important;
                }
            }
        `;
        document.head.appendChild(printFontStyle);

        console.log('تم تهيئة صفحة تفاصيل الطلب بنجاح!');
    } catch (error) {
        console.error('حدث خطأ أثناء تهيئة صفحة تفاصيل الطلب:', error);
    }
});

// دالة طباعة بسيطة ومحسنة
function printOrder() {
    console.log('بدء عملية طباعة الطلب...');

    // حفظ العنوان الأصلي
    const originalTitle = document.title;

    // تغيير عنوان الصفحة للطباعة
    document.title = 'طلب #<?php echo e($order->branch_serial ?? $order->order_number ?? $order->id); ?> - <?php echo e($order->customer_name ?? "غير محدد"); ?>';

    // إضافة كلاس للطباعة
    document.body.classList.add('printing');

    // معالج ما بعد الطباعة
    window.onafterprint = function() {
        document.title = originalTitle;
        document.body.classList.remove('printing');
        window.onafterprint = null;
        console.log('انتهاء عملية الطباعة');
    };

    // تنفيذ الطباعة
    setTimeout(() => {
        window.print();
    }, 100);
}
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp2\htdocs\app\resources\views/orders/show.blade.php ENDPATH**/ ?>