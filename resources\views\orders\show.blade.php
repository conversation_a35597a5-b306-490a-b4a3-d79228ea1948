@extends('layouts.admin')

@section('title', 'تفاصيل الطلب')

@section('styles')
<style>
    /* إعدادات الطباعة المحسنة والمنسقة */
    @media print {
        @page {
            size: A4 portrait;
            margin: 15mm 10mm;
        }

        * {
            -webkit-print-color-adjust: exact !important;
            color-adjust: exact !important;
            print-color-adjust: exact !important;
            box-sizing: border-box !important;
        }

        html, body {
            width: 100% !important;
            height: auto !important;
            margin: 0 !important;
            padding: 0 !important;
            font-family: 'Cairo', 'Tahoma', 'Arial', sans-serif !important;
            font-size: 13px !important;
            line-height: 1.3 !important;
            direction: rtl !important;
            text-align: right !important;
            background: white !important;
            color: black !important;
            overflow: visible !important;
        }

        /* إخفاء العناصر غير المرغوبة */
        .no-print,
        .action-buttons,
        .navbar,
        .sidebar,
        footer,
        nav,
        .btn,
        header,
        .breadcrumb,
        .pagination {
            display: none !important;
            visibility: hidden !important;
        }

        /* إظهار المحتوى الرئيسي */
        .page-container,
        .invoice-container,
        .invoice-header,
        .company-info,
        .company-text,
        .company-name,
        .company-subtitle,
        .invoice-details,
        .invoice-number,
        .invoice-date,
        .details-table,
        .details-table tr,
        .details-table td,
        .label-col,
        .value-col,
        .section-title {
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
            position: static !important;
            overflow: visible !important;
        }

        .page-container {
            max-width: none !important;
            margin: 0 !important;
            padding: 0 !important;
        }

        .invoice-container {
            background: white !important;
            box-shadow: none !important;
            border-radius: 0 !important;
            margin: 0 !important;
            padding: 0 !important;
            border: none !important;
        }

        .invoice-header {
            background: white !important;
            color: black !important;
            border: 3px solid black !important;
            padding: 15px !important;
            margin-bottom: 15px !important;
            display: flex !important;
            justify-content: space-between !important;
            align-items: center !important;
            page-break-inside: avoid !important;
        }

        .company-info {
            display: flex !important;
            align-items: center !important;
            gap: 15px !important;
            flex: 1 !important;
        }

        .company-logo {
            width: 60px !important;
            height: 60px !important;
            border: 2px solid black !important;
            border-radius: 0 !important;
            background: white !important;
            padding: 3px !important;
            object-fit: contain !important;
        }

        .company-text {
            flex: 1 !important;
        }

        .company-name {
            font-size: 18px !important;
            font-weight: bold !important;
            color: black !important;
            margin-bottom: 5px !important;
            text-align: right !important;
        }

        .company-subtitle {
            font-size: 14px !important;
            color: black !important;
            text-align: right !important;
        }

        .invoice-details {
            text-align: center !important;
            background: white !important;
            border: 2px solid black !important;
            padding: 12px !important;
            border-radius: 0 !important;
            min-width: 150px !important;
        }

        .invoice-number {
            font-size: 16px !important;
            font-weight: bold !important;
            color: black !important;
            margin-bottom: 5px !important;
        }

        .invoice-date {
            font-size: 13px !important;
            color: black !important;
        }

        .details-table {
            width: 100% !important;
            border-collapse: collapse !important;
            border: 3px solid black !important;
            margin: 15px 0 !important;
            font-size: 13px !important;
            display: table !important;
            page-break-inside: avoid !important;
        }

        .details-table tr {
            display: table-row !important;
            page-break-inside: avoid !important;
            min-height: 25px !important;
        }

        .details-table td {
            display: table-cell !important;
            border: 1px solid black !important;
            padding: 10px 8px !important;
            background: white !important;
            color: black !important;
            vertical-align: middle !important;
            text-align: right !important;
            word-wrap: break-word !important;
        }

        .label-col {
            background: #f0f0f0 !important;
            font-weight: bold !important;
            width: 25% !important;
            color: black !important;
            border-right: 2px solid black !important;
        }

        .value-col {
            background: white !important;
            width: 25% !important;
            color: black !important;
            font-weight: normal !important;
        }

        .section-title {
            background: #e8e8e8 !important;
            color: black !important;
            font-weight: bold !important;
            text-align: center !important;
            padding: 12px !important;
            font-size: 14px !important;
            border: 2px solid black !important;
            letter-spacing: 1px !important;
            position: relative !important;
        }

        .section-title::before {
            content: '' !important;
            position: absolute !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            bottom: 0 !important;
            background: linear-gradient(45deg, transparent 49%, #ccc 50%, transparent 51%) !important;
            opacity: 0.1 !important;
        }

        /* تنسيق قسم التوقيعات المحسن */
        .signature-section {
            display: flex !important;
            justify-content: space-between !important;
            margin: 20px 0 !important;
            gap: 30px !important;
            page-break-inside: avoid !important;
        }

        .signature-box {
            flex: 1 !important;
            text-align: center !important;
            padding: 15px !important;
            background: white !important;
            border: 2px solid black !important;
            border-radius: 0 !important;
            min-height: 100px !important;
        }

        .signature-box h5 {
            font-size: 14px !important;
            font-weight: bold !important;
            color: black !important;
            margin: 0 0 15px 0 !important;
            text-decoration: underline !important;
        }

        .signature-line {
            border-bottom: 2px solid black !important;
            height: 50px !important;
            margin: 15px 0 !important;
            position: relative !important;
        }

        .signature-box small {
            font-size: 11px !important;
            color: black !important;
            font-weight: normal !important;
        }

        /* تذييل الفاتورة المحسن */
        .invoice-footer {
            background: white !important;
            border: 2px solid black !important;
            text-align: center !important;
            padding: 15px !important;
            margin: 20px 0 0 0 !important;
            border-radius: 0 !important;
            page-break-inside: avoid !important;
        }

        .invoice-footer p {
            margin: 8px 0 !important;
            color: black !important;
            font-size: 13px !important;
            line-height: 1.4 !important;
        }

        .invoice-footer strong {
            font-weight: bold !important;
            color: black !important;
            font-size: 14px !important;
        }

        .invoice-footer small {
            font-size: 10px !important;
            color: #666 !important;
            display: block !important;
            margin-top: 10px !important;
            border-top: 1px solid #ccc !important;
            padding-top: 8px !important;
        }

        /* إصلاح إضافي لضمان ظهور المحتوى */
        body * {
            visibility: visible !important;
            opacity: 1 !important;
        }

        .page-container,
        .invoice-container,
        .invoice-header,
        .details-table,
        .details-table *,
        div,
        p,
        h1, h2, h3, h4, h5, h6,
        span,
        img {
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
            position: static !important;
            overflow: visible !important;
        }

        table {
            display: table !important;
        }

        tr {
            display: table-row !important;
        }

        td {
            display: table-cell !important;
        }

        img {
            display: inline-block !important;
        }

        /* إزالة أي تأثيرات قد تخفي المحتوى */
        * {
            transform: none !important;
            filter: none !important;
            backdrop-filter: none !important;
            -webkit-backdrop-filter: none !important;
        }
    }

    /* أنماط الشاشة */
    @media screen {
        body {
            font-family: 'Cairo', 'Segoe UI', 'Tahoma', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            background-attachment: fixed;
            direction: rtl;
            text-align: right;
            margin: 0;
            padding: 20px;
            font-size: 14px;
            line-height: 1.6;
            min-height: 100vh;
        }

        .page-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .action-buttons {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            padding: 2rem;
            border-radius: 20px;
            box-shadow: 0 15px 50px rgba(0, 0, 0, 0.15);
            margin-bottom: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .invoice-container {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.2);
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .invoice-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .company-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .company-logo {
            width: 60px;
            height: 60px;
            border-radius: 10px;
            background: white;
            padding: 5px;
        }

        .company-name {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .company-subtitle {
            font-size: 1rem;
            opacity: 0.9;
        }

        .invoice-details {
            text-align: center;
            background: rgba(255, 255, 255, 0.2);
            padding: 1rem;
            border-radius: 10px;
        }

        .invoice-number {
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .details-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            margin: 2rem;
            font-size: 15px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 15px 50px rgba(0, 0, 0, 0.15);
            background: white;
        }

        .details-table td {
            border: 1px solid #e2e8f0;
            padding: 1rem;
            vertical-align: top;
        }

        .label-col {
            background: #f8fafc;
            font-weight: 600;
            color: #2d3748;
            width: 25%;
        }

        .value-col {
            background: white;
            color: #4a5568;
            width: 25%;
        }

        .section-title {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: bold;
            text-align: center;
            padding: 1rem;
            font-size: 1.1rem;
        }

        .btn {
            border-radius: 12px;
            padding: 0.8rem 1.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-warning {
            background: #ffc107;
            color: #212529;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }
    }
</style>
@endsection

@section('content')
<div class="page-container">
    <!-- أزرار الإجراءات -->
    <div class="action-buttons no-print">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h4 class="mb-0">تفاصيل الطلب: {{ $order->branch_serial ?? $order->order_number ?? '#' . $order->id }}</h4>
                <small class="text-muted">تم الإنشاء في: {{ $order->created_at->format('Y-m-d H:i') }}</small>
            </div>
            <div class="d-flex gap-2">
                <a href="{{ route('orders.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i> العودة للقائمة
                </a>
                <a href="{{ route('orders.edit', $order) }}" class="btn btn-warning">
                    <i class="fas fa-edit"></i> تعديل
                </a>
                <button class="btn btn-primary" onclick="simplePrint()">
                    <i class="fas fa-print"></i> طباعة
                </button>
                <button class="btn btn-info" onclick="forcePrint()" style="margin-right: 10px;">
                    <i class="fas fa-print"></i> طباعة مباشرة
                </button>
            </div>
        </div>
    </div>

    <!-- حاوية الفاتورة -->
    <div class="invoice-container">
        <!-- رأس الفاتورة -->
        <div class="invoice-header">
            <div class="company-info">
                <img src="{{ asset('images/logos/aswsd.png') }}" alt="شعار الشركة" class="company-logo">
                <div class="company-text">
                    <div class="company-name">مجموعة أبراهيم الاحمدي اليمنية</div>
                    <div class="company-subtitle">للتجارة والخدمات اللوجستية</div>
                </div>
            </div>
            <div class="invoice-details">
                <div class="invoice-number">طلب #{{ $order->branch_serial ?? $order->order_number ?? $order->id }}</div>
                <div class="invoice-date">{{ $order->created_at->format('Y/m/d') }}</div>
            </div>
        </div>

        <!-- جدول تفاصيل الطلب -->
        <table class="details-table">
            <tr>
                <td colspan="4" class="section-title">معلومات الطلب الأساسية</td>
            </tr>
            <tr>
                <td class="label-col">رقم الطلب</td>
                <td class="value-col">{{ $order->branch_serial ?? $order->order_number ?? $order->id }}</td>
                <td class="label-col">التاريخ</td>
                <td class="value-col">{{ $order->request_date ?? $order->created_at->format('Y-m-d') }}</td>
            </tr>
            <tr>
                <td class="label-col">اسم العميل</td>
                <td class="value-col">{{ $order->customer_name ?? 'غير محدد' }}</td>
                <td class="label-col">رقم الهاتف</td>
                <td class="value-col">{{ $order->phone_number ?? 'غير محدد' }}</td>
            </tr>
            <tr>
                <td class="label-col">اسم المستلم</td>
                <td class="value-col">{{ $order->recipient_name ?? 'غير محدد' }}</td>
                <td class="label-col">نوع الخدمة</td>
                <td class="value-col">{{ $order->service_type ?? 'غير محدد' }}</td>
            </tr>

            <tr>
                <td colspan="4" class="section-title">تفاصيل البضاعة</td>
            </tr>
            <tr>
                <td class="label-col">نوع البضاعة</td>
                <td class="value-col">{{ $order->goods_name ?? 'غير محدد' }}</td>
                <td class="label-col">بلد المنشأ</td>
                <td class="value-col">{{ $order->country_of_origin ?? 'غير محدد' }}</td>
            </tr>
            <tr>
                <td class="label-col">الوزن</td>
                <td class="value-col">{{ $order->weight ?? 'غير محدد' }}</td>
                <td class="label-col">الكمية</td>
                <td class="value-col">{{ $order->quantity ?? 'غير محدد' }}</td>
            </tr>
            <tr>
                <td class="label-col">منطقة المغادرة</td>
                <td class="value-col">{{ $order->departure_area ?? 'غير محدد' }}</td>
                <td class="label-col">منطقة التسليم</td>
                <td class="value-col">{{ $order->delivery_area ?? 'غير محدد' }}</td>
            </tr>

            <tr>
                <td colspan="4" class="section-title">التكاليف والأسعار</td>
            </tr>
            <tr>
                <td class="label-col">سعر الشراء</td>
                <td class="value-col">{{ number_format($order->purchase_price ?? 0, 2) }} ريال</td>
                <td class="label-col">سعر البيع</td>
                <td class="value-col">{{ number_format($order->selling_price ?? 0, 2) }} ريال</td>
            </tr>
            <tr>
                <td class="label-col">الربح</td>
                <td class="value-col">{{ number_format(($order->selling_price ?? 0) - ($order->purchase_price ?? 0), 2) }} ريال</td>
                <td class="label-col">حالة الطلب</td>
                <td class="value-col">{{ $order->status ?? 'غير محدد' }}</td>
            </tr>

            @if($order->notes)
            <tr>
                <td class="label-col">ملاحظات</td>
                <td colspan="3" class="value-col">{{ $order->notes }}</td>
            </tr>
            @endif
        </table>

        <!-- قسم التوقيعات المحسن -->
        <div class="signature-section">
            <div class="signature-box">
                <h5>توقيع العميل</h5>
                <div class="signature-line"></div>
                <small>التاريخ: ___________</small>
            </div>
            <div class="signature-box">
                <h5>توقيع المسؤول</h5>
                <div class="signature-line"></div>
                <small>التاريخ: ___________</small>
            </div>
        </div>

        <!-- تذييل الفاتورة المحسن -->
        <div class="invoice-footer">
            <p><strong>مجموعة أبراهيم الاحمدي اليمنية</strong></p>
            <p>للتجارة والخدمات اللوجستية</p>
            <p>شكراً لثقتكم بنا</p>
            <small>تم إنشاء هذا المستند في: {{ now()->format('Y-m-d H:i:s') }}</small>
        </div>
    </div>
</div>

<script>
// دالة طباعة بسيطة جداً
function simplePrint() {
    console.log('طباعة بسيطة...');

    // حفظ العنوان الأصلي
    const originalTitle = document.title;
    document.title = 'طلب #{{ $order->branch_serial ?? $order->order_number ?? $order->id }}';

    // طباعة مباشرة
    window.print();

    // استعادة العنوان
    setTimeout(() => {
        document.title = originalTitle;
    }, 1000);
}

// دالة طباعة قسرية محسنة
function forcePrint() {
    console.log('طباعة قسرية محسنة...');

    // حفظ العنوان الأصلي
    const originalTitle = document.title;
    document.title = 'طلب #{{ $order->branch_serial ?? $order->order_number ?? $order->id }} - مجموعة أبراهيم الاحمدي';

    // إضافة أنماط طباعة محسنة
    const forceStyle = document.createElement('style');
    forceStyle.id = 'force-print-style';
    forceStyle.textContent = `
        @media print {
            @page { size: A4 portrait; margin: 15mm 10mm; }
            body { font-family: 'Cairo', 'Tahoma', 'Arial', sans-serif !important; font-size: 13px !important; }
            body * { visibility: visible !important; opacity: 1 !important; }
            .no-print, .action-buttons, .navbar, .sidebar, footer, nav, .btn { display: none !important; }
            .page-container, .invoice-container { display: block !important; }
            .invoice-header { display: flex !important; border: 3px solid black !important; }
            .details-table { display: table !important; border: 3px solid black !important; }
            .details-table tr { display: table-row !important; }
            .details-table td { display: table-cell !important; border: 1px solid black !important; padding: 10px 8px !important; }
            .signature-section { display: flex !important; }
            .signature-box { border: 2px solid black !important; }
            .invoice-footer { border: 2px solid black !important; }
        }
    `;
    document.head.appendChild(forceStyle);

    // طباعة
    setTimeout(() => {
        window.print();
        // استعادة العنوان وإزالة الأنماط
        setTimeout(() => {
            document.title = originalTitle;
            const style = document.getElementById('force-print-style');
            if (style) style.remove();
        }, 2000);
    }, 100);
}

// دالة طباعة محسنة لحل مشكلة الطباعة الفارغة
function printOrder() {
    console.log('بدء عملية طباعة الطلب...');

    // حفظ العنوان الأصلي
    const originalTitle = document.title;

    // تغيير عنوان الصفحة للطباعة
    document.title = 'طلب #{{ $order->branch_serial ?? $order->order_number ?? $order->id }} - {{ $order->customer_name ?? "غير محدد" }}';

    // إضافة كلاس للطباعة
    document.body.classList.add('printing');
    document.documentElement.classList.add('printing');

    // إظهار المحتوى بشكل صريح
    const elementsToShow = [
        '.page-container',
        '.invoice-container',
        '.invoice-header',
        '.company-info',
        '.company-text',
        '.company-name',
        '.company-subtitle',
        '.invoice-details',
        '.invoice-number',
        '.invoice-date',
        '.details-table',
        '.details-table tr',
        '.details-table td',
        '.label-col',
        '.value-col',
        '.section-title'
    ];

    elementsToShow.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
            element.style.display = element.tagName.toLowerCase() === 'table' ? 'table' :
                                   element.tagName.toLowerCase() === 'tr' ? 'table-row' :
                                   element.tagName.toLowerCase() === 'td' ? 'table-cell' : 'block';
            element.style.visibility = 'visible';
            element.style.opacity = '1';
            element.style.position = 'static';
            element.style.overflow = 'visible';
        });
    });

    // معالج ما بعد الطباعة
    window.onafterprint = function() {
        document.title = originalTitle;
        document.body.classList.remove('printing');
        document.documentElement.classList.remove('printing');
        window.onafterprint = null;
        console.log('انتهاء عملية الطباعة');
    };

    // تنفيذ الطباعة بعد تأخير قصير
    setTimeout(() => {
        window.print();
    }, 500);
}

// تهيئة الصفحة عند التحميل
document.addEventListener('DOMContentLoaded', function() {
    console.log('تهيئة صفحة تفاصيل الطلب...');

    // التأكد من وجود زر الطباعة
    const printButton = document.querySelector('button[onclick="window.print()"]');
    if (printButton) {
        printButton.onclick = printOrder;
        console.log('تم ربط زر الطباعة بالدالة المحسنة');
    }

    // إضافة أنماط إضافية للطباعة
    const printStyles = document.createElement('style');
    printStyles.id = 'enhanced-print-styles';
    printStyles.textContent = `
        @media print {
            /* ضمان إظهار جميع العناصر بتنسيق محسن */
            * {
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
                color-adjust: exact !important;
            }

            .page-container * {
                visibility: visible !important;
                opacity: 1 !important;
            }

            .invoice-container * {
                display: block !important;
                visibility: visible !important;
                opacity: 1 !important;
            }

            table, tr, td {
                display: table !important;
                visibility: visible !important;
                opacity: 1 !important;
            }

            tr {
                display: table-row !important;
            }

            td {
                display: table-cell !important;
            }

            /* تحسينات إضافية للطباعة */
            .invoice-header {
                border: 3px solid black !important;
                margin-bottom: 15px !important;
            }

            .details-table {
                border: 3px solid black !important;
                margin: 15px 0 !important;
            }

            .signature-section {
                margin: 20px 0 !important;
            }

            .signature-box {
                border: 2px solid black !important;
            }

            .invoice-footer {
                border: 2px solid black !important;
                margin-top: 20px !important;
            }
        }
    `;
    document.head.appendChild(printStyles);

    console.log('تم تهيئة الصفحة بنجاح');
});
</script>

@endsection
