@extends('layouts.admin')

@section('title', 'تفاصيل الطلب')

@section('styles')
<style>
    /* إعدادات الطباعة المحسنة */
    @media print {
        @page {
            size: A4 portrait;
            margin: 10mm;
        }

        * {
            -webkit-print-color-adjust: exact !important;
            color-adjust: exact !important;
            print-color-adjust: exact !important;
            box-sizing: border-box !important;
        }

        html, body {
            width: 100% !important;
            height: auto !important;
            margin: 0 !important;
            padding: 0 !important;
            font-family: 'Cairo', 'Tahoma', 'Arial', sans-serif !important;
            font-size: 14px !important;
            line-height: 1.4 !important;
            direction: rtl !important;
            text-align: right !important;
            background: white !important;
            color: black !important;
            overflow: visible !important;
        }

        /* إخفاء العناصر غير المرغوبة */
        .no-print,
        .action-buttons,
        .navbar,
        .sidebar,
        footer,
        nav,
        .btn,
        header,
        .breadcrumb,
        .pagination {
            display: none !important;
            visibility: hidden !important;
        }

        /* إظهار المحتوى الرئيسي */
        .page-container,
        .invoice-container,
        .invoice-header,
        .company-info,
        .company-text,
        .company-name,
        .company-subtitle,
        .invoice-details,
        .invoice-number,
        .invoice-date,
        .details-table,
        .details-table tr,
        .details-table td,
        .label-col,
        .value-col,
        .section-title {
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
            position: static !important;
            overflow: visible !important;
        }

        .page-container {
            max-width: none !important;
            margin: 0 !important;
            padding: 0 !important;
        }

        .invoice-container {
            background: white !important;
            box-shadow: none !important;
            border-radius: 0 !important;
            margin: 0 !important;
            padding: 0 !important;
            border: none !important;
        }

        .invoice-header {
            background: white !important;
            color: black !important;
            border-bottom: 2px solid black !important;
            padding: 10px !important;
            margin-bottom: 10px !important;
            display: flex !important;
            justify-content: space-between !important;
            align-items: center !important;
        }

        .company-info {
            display: flex !important;
            align-items: center !important;
            gap: 10px !important;
        }

        .company-logo {
            width: 50px !important;
            height: 50px !important;
            border: 1px solid black !important;
            border-radius: 0 !important;
            background: white !important;
            padding: 2px !important;
        }

        .company-name {
            font-size: 16px !important;
            font-weight: bold !important;
            color: black !important;
            margin-bottom: 2px !important;
        }

        .company-subtitle {
            font-size: 12px !important;
            color: black !important;
        }

        .invoice-details {
            text-align: center !important;
            background: white !important;
            border: 1px solid black !important;
            padding: 8px !important;
            border-radius: 0 !important;
        }

        .invoice-number {
            font-size: 14px !important;
            font-weight: bold !important;
            color: black !important;
            margin-bottom: 2px !important;
        }

        .invoice-date {
            font-size: 12px !important;
            color: black !important;
        }

        .details-table {
            width: 100% !important;
            border-collapse: collapse !important;
            border: 2px solid black !important;
            margin: 10px 0 !important;
            font-size: 12px !important;
            display: table !important;
        }

        .details-table tr {
            display: table-row !important;
            page-break-inside: avoid !important;
        }

        .details-table td {
            display: table-cell !important;
            border: 1px solid black !important;
            padding: 6px !important;
            background: white !important;
            color: black !important;
            vertical-align: top !important;
            text-align: right !important;
        }

        .label-col {
            background: #f5f5f5 !important;
            font-weight: bold !important;
            width: 25% !important;
        }

        .value-col {
            background: white !important;
            width: 25% !important;
        }

        .section-title {
            background: #e0e0e0 !important;
            color: black !important;
            font-weight: bold !important;
            text-align: center !important;
            padding: 8px !important;
            font-size: 13px !important;
        }

        /* إظهار قسم التوقيعات */
        div[style*="display: flex"] {
            display: flex !important;
            justify-content: space-between !important;
            margin: 15px 0 !important;
            gap: 20px !important;
        }

        div[style*="flex: 1"] {
            flex: 1 !important;
            text-align: center !important;
            padding: 10px !important;
            background: white !important;
            border: 1px solid black !important;
            border-radius: 0 !important;
        }

        h5 {
            font-size: 12px !important;
            font-weight: bold !important;
            color: black !important;
            margin: 0 0 10px 0 !important;
        }

        div[style*="border-bottom: 2px dashed"] {
            border-bottom: 2px dashed black !important;
            height: 40px !important;
            margin: 10px 0 !important;
        }

        small {
            font-size: 10px !important;
            color: black !important;
        }

        /* تذييل الفاتورة */
        div[style*="background: #f8f9fa"] {
            background: white !important;
            border: 1px solid black !important;
            text-align: center !important;
            padding: 10px !important;
            margin: 10px 0 !important;
            border-radius: 0 !important;
        }

        p {
            margin: 5px 0 !important;
            color: black !important;
            font-size: 12px !important;
        }

        strong {
            font-weight: bold !important;
            color: black !important;
        }

        /* إصلاح إضافي لضمان ظهور المحتوى */
        body * {
            visibility: visible !important;
            opacity: 1 !important;
        }

        .page-container,
        .invoice-container,
        .invoice-header,
        .details-table,
        .details-table *,
        div,
        p,
        h1, h2, h3, h4, h5, h6,
        span,
        img {
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
            position: static !important;
            overflow: visible !important;
        }

        table {
            display: table !important;
        }

        tr {
            display: table-row !important;
        }

        td {
            display: table-cell !important;
        }

        img {
            display: inline-block !important;
        }

        /* إزالة أي تأثيرات قد تخفي المحتوى */
        * {
            transform: none !important;
            filter: none !important;
            backdrop-filter: none !important;
            -webkit-backdrop-filter: none !important;
        }
    }

    /* أنماط الشاشة */
    @media screen {
        body {
            font-family: 'Cairo', 'Segoe UI', 'Tahoma', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            background-attachment: fixed;
            direction: rtl;
            text-align: right;
            margin: 0;
            padding: 20px;
            font-size: 14px;
            line-height: 1.6;
            min-height: 100vh;
        }

        .page-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .action-buttons {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            padding: 2rem;
            border-radius: 20px;
            box-shadow: 0 15px 50px rgba(0, 0, 0, 0.15);
            margin-bottom: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .invoice-container {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.2);
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .invoice-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .company-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .company-logo {
            width: 60px;
            height: 60px;
            border-radius: 10px;
            background: white;
            padding: 5px;
        }

        .company-name {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .company-subtitle {
            font-size: 1rem;
            opacity: 0.9;
        }

        .invoice-details {
            text-align: center;
            background: rgba(255, 255, 255, 0.2);
            padding: 1rem;
            border-radius: 10px;
        }

        .invoice-number {
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .details-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            margin: 2rem;
            font-size: 15px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 15px 50px rgba(0, 0, 0, 0.15);
            background: white;
        }

        .details-table td {
            border: 1px solid #e2e8f0;
            padding: 1rem;
            vertical-align: top;
        }

        .label-col {
            background: #f8fafc;
            font-weight: 600;
            color: #2d3748;
            width: 25%;
        }

        .value-col {
            background: white;
            color: #4a5568;
            width: 25%;
        }

        .section-title {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: bold;
            text-align: center;
            padding: 1rem;
            font-size: 1.1rem;
        }

        .btn {
            border-radius: 12px;
            padding: 0.8rem 1.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-warning {
            background: #ffc107;
            color: #212529;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }
    }
</style>
@endsection

@section('content')
<div class="page-container">
    <!-- أزرار الإجراءات -->
    <div class="action-buttons no-print">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h4 class="mb-0">تفاصيل الطلب: {{ $order->branch_serial ?? $order->order_number ?? '#' . $order->id }}</h4>
                <small class="text-muted">تم الإنشاء في: {{ $order->created_at->format('Y-m-d H:i') }}</small>
            </div>
            <div class="d-flex gap-2">
                <a href="{{ route('orders.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i> العودة للقائمة
                </a>
                <a href="{{ route('orders.edit', $order) }}" class="btn btn-warning">
                    <i class="fas fa-edit"></i> تعديل
                </a>
                <button class="btn btn-primary" onclick="simplePrint()">
                    <i class="fas fa-print"></i> طباعة
                </button>
                <button class="btn btn-info" onclick="forcePrint()" style="margin-right: 10px;">
                    <i class="fas fa-print"></i> طباعة مباشرة
                </button>
            </div>
        </div>
    </div>

    <!-- حاوية الفاتورة -->
    <div class="invoice-container">
        <!-- رأس الفاتورة -->
        <div class="invoice-header">
            <div class="company-info">
                <img src="{{ asset('images/logos/aswsd.png') }}" alt="شعار الشركة" class="company-logo">
                <div class="company-text">
                    <div class="company-name">مجموعة أبراهيم الاحمدي اليمنية</div>
                    <div class="company-subtitle">للتجارة والخدمات اللوجستية</div>
                </div>
            </div>
            <div class="invoice-details">
                <div class="invoice-number">طلب #{{ $order->branch_serial ?? $order->order_number ?? $order->id }}</div>
                <div class="invoice-date">{{ $order->created_at->format('Y/m/d') }}</div>
            </div>
        </div>

        <!-- جدول تفاصيل الطلب -->
        <table class="details-table">
            <tr>
                <td colspan="4" class="section-title">معلومات الطلب الأساسية</td>
            </tr>
            <tr>
                <td class="label-col">رقم الطلب</td>
                <td class="value-col">{{ $order->branch_serial ?? $order->order_number ?? $order->id }}</td>
                <td class="label-col">التاريخ</td>
                <td class="value-col">{{ $order->request_date ?? $order->created_at->format('Y-m-d') }}</td>
            </tr>
            <tr>
                <td class="label-col">اسم العميل</td>
                <td class="value-col">{{ $order->customer_name ?? 'غير محدد' }}</td>
                <td class="label-col">رقم الهاتف</td>
                <td class="value-col">{{ $order->phone_number ?? 'غير محدد' }}</td>
            </tr>
            <tr>
                <td class="label-col">اسم المستلم</td>
                <td class="value-col">{{ $order->recipient_name ?? 'غير محدد' }}</td>
                <td class="label-col">نوع الخدمة</td>
                <td class="value-col">{{ $order->service_type ?? 'غير محدد' }}</td>
            </tr>

            <tr>
                <td colspan="4" class="section-title">تفاصيل البضاعة</td>
            </tr>
            <tr>
                <td class="label-col">نوع البضاعة</td>
                <td class="value-col">{{ $order->goods_name ?? 'غير محدد' }}</td>
                <td class="label-col">بلد المنشأ</td>
                <td class="value-col">{{ $order->country_of_origin ?? 'غير محدد' }}</td>
            </tr>
            <tr>
                <td class="label-col">الوزن</td>
                <td class="value-col">{{ $order->weight ?? 'غير محدد' }}</td>
                <td class="label-col">الكمية</td>
                <td class="value-col">{{ $order->quantity ?? 'غير محدد' }}</td>
            </tr>
            <tr>
                <td class="label-col">منطقة المغادرة</td>
                <td class="value-col">{{ $order->departure_area ?? 'غير محدد' }}</td>
                <td class="label-col">منطقة التسليم</td>
                <td class="value-col">{{ $order->delivery_area ?? 'غير محدد' }}</td>
            </tr>

            <tr>
                <td colspan="4" class="section-title">التكاليف والأسعار</td>
            </tr>
            <tr>
                <td class="label-col">سعر الشراء</td>
                <td class="value-col">{{ number_format($order->purchase_price ?? 0, 2) }} ريال</td>
                <td class="label-col">سعر البيع</td>
                <td class="value-col">{{ number_format($order->selling_price ?? 0, 2) }} ريال</td>
            </tr>
            <tr>
                <td class="label-col">الربح</td>
                <td class="value-col">{{ number_format(($order->selling_price ?? 0) - ($order->purchase_price ?? 0), 2) }} ريال</td>
                <td class="label-col">حالة الطلب</td>
                <td class="value-col">{{ $order->status ?? 'غير محدد' }}</td>
            </tr>

            @if($order->notes)
            <tr>
                <td class="label-col">ملاحظات</td>
                <td colspan="3" class="value-col">{{ $order->notes }}</td>
            </tr>
            @endif
        </table>

        <!-- قسم التوقيعات -->
        <div style="display: flex; justify-content: space-between; margin: 2rem; gap: 2rem;">
            <div style="flex: 1; text-align: center; padding: 1rem; background: #f8f9fa; border-radius: 10px;">
                <h5>توقيع العميل</h5>
                <div style="border-bottom: 2px dashed #ccc; height: 60px; margin: 1rem 0;"></div>
                <small>التاريخ: ___________</small>
            </div>
            <div style="flex: 1; text-align: center; padding: 1rem; background: #f8f9fa; border-radius: 10px;">
                <h5>توقيع المسؤول</h5>
                <div style="border-bottom: 2px dashed #ccc; height: 60px; margin: 1rem 0;"></div>
                <small>التاريخ: ___________</small>
            </div>
        </div>

        <!-- تذييل الفاتورة -->
        <div style="margin: 2rem; padding: 1.5rem; background: #f8f9fa; text-align: center; border-radius: 10px;">
            <p><strong>مجموعة أبراهيم الاحمدي اليمنية للتجارة والخدمات اللوجستية</strong></p>
            <p>شكراً لثقتكم بنا</p>
            <small>تم إنشاء هذا المستند في: {{ now()->format('Y-m-d H:i:s') }}</small>
        </div>
    </div>
</div>

<script>
// دالة طباعة بسيطة جداً
function simplePrint() {
    console.log('طباعة بسيطة...');

    // حفظ العنوان الأصلي
    const originalTitle = document.title;
    document.title = 'طلب #{{ $order->branch_serial ?? $order->order_number ?? $order->id }}';

    // طباعة مباشرة
    window.print();

    // استعادة العنوان
    setTimeout(() => {
        document.title = originalTitle;
    }, 1000);
}

// دالة طباعة قسرية
function forcePrint() {
    console.log('طباعة قسرية...');

    // إضافة أنماط طباعة مباشرة
    const forceStyle = document.createElement('style');
    forceStyle.id = 'force-print-style';
    forceStyle.textContent = `
        @media print {
            body * { visibility: visible !important; opacity: 1 !important; }
            .no-print, .action-buttons, .navbar, .sidebar, footer, nav, .btn { display: none !important; }
            .page-container, .invoice-container { display: block !important; }
            table { display: table !important; }
            tr { display: table-row !important; }
            td { display: table-cell !important; }
        }
    `;
    document.head.appendChild(forceStyle);

    // طباعة
    setTimeout(() => {
        window.print();
        // إزالة الأنماط بعد الطباعة
        setTimeout(() => {
            const style = document.getElementById('force-print-style');
            if (style) style.remove();
        }, 2000);
    }, 100);
}

// دالة طباعة محسنة لحل مشكلة الطباعة الفارغة
function printOrder() {
    console.log('بدء عملية طباعة الطلب...');

    // حفظ العنوان الأصلي
    const originalTitle = document.title;

    // تغيير عنوان الصفحة للطباعة
    document.title = 'طلب #{{ $order->branch_serial ?? $order->order_number ?? $order->id }} - {{ $order->customer_name ?? "غير محدد" }}';

    // إضافة كلاس للطباعة
    document.body.classList.add('printing');
    document.documentElement.classList.add('printing');

    // إظهار المحتوى بشكل صريح
    const elementsToShow = [
        '.page-container',
        '.invoice-container',
        '.invoice-header',
        '.company-info',
        '.company-text',
        '.company-name',
        '.company-subtitle',
        '.invoice-details',
        '.invoice-number',
        '.invoice-date',
        '.details-table',
        '.details-table tr',
        '.details-table td',
        '.label-col',
        '.value-col',
        '.section-title'
    ];

    elementsToShow.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
            element.style.display = element.tagName.toLowerCase() === 'table' ? 'table' :
                                   element.tagName.toLowerCase() === 'tr' ? 'table-row' :
                                   element.tagName.toLowerCase() === 'td' ? 'table-cell' : 'block';
            element.style.visibility = 'visible';
            element.style.opacity = '1';
            element.style.position = 'static';
            element.style.overflow = 'visible';
        });
    });

    // معالج ما بعد الطباعة
    window.onafterprint = function() {
        document.title = originalTitle;
        document.body.classList.remove('printing');
        document.documentElement.classList.remove('printing');
        window.onafterprint = null;
        console.log('انتهاء عملية الطباعة');
    };

    // تنفيذ الطباعة بعد تأخير قصير
    setTimeout(() => {
        window.print();
    }, 500);
}

// تهيئة الصفحة عند التحميل
document.addEventListener('DOMContentLoaded', function() {
    console.log('تهيئة صفحة تفاصيل الطلب...');

    // التأكد من وجود زر الطباعة
    const printButton = document.querySelector('button[onclick="window.print()"]');
    if (printButton) {
        printButton.onclick = printOrder;
        console.log('تم ربط زر الطباعة بالدالة المحسنة');
    }

    // إضافة أنماط إضافية للطباعة
    const printStyles = document.createElement('style');
    printStyles.id = 'enhanced-print-styles';
    printStyles.textContent = `
        @media print {
            /* ضمان إظهار جميع العناصر */
            * {
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
                color-adjust: exact !important;
            }

            .page-container * {
                visibility: visible !important;
                opacity: 1 !important;
            }

            .invoice-container * {
                display: block !important;
                visibility: visible !important;
                opacity: 1 !important;
            }

            table, tr, td {
                display: table !important;
                visibility: visible !important;
                opacity: 1 !important;
            }

            tr {
                display: table-row !important;
            }

            td {
                display: table-cell !important;
            }
        }
    `;
    document.head.appendChild(printStyles);

    console.log('تم تهيئة الصفحة بنجاح');
});
</script>

@endsection
