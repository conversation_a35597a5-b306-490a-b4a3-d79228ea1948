/**
 * تحسين الطباعة لورقة A4
 * Print Optimization for A4 Paper
 */

// تحسين الطباعة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // إضافة أزرار الطباعة المحسنة
    addPrintButtons();
    
    // تحسين الطباعة التلقائي
    optimizePrintLayout();
    
    // إضافة مستمعات الأحداث
    addPrintEventListeners();
    
    // تسجيل حالة الطباعة في وحدة التحكم للتشخيص
    console.log('تم تحميل تحسينات الطباعة');
});

/**
 * إضافة أزرار الطباعة المحسنة
 */
function addPrintButtons() {
    const printButtons = document.querySelectorAll('[onclick*="window.print"], [onclick*="print"], .print-btn, button:has(i.fa-print)');
    
    printButtons.forEach(button => {
        // إضافة كلاس للتحكم في الطباعة
        button.classList.add('print-optimized-btn');
        
        // تحديث وظيفة الطباعة
        button.onclick = function(e) {
            e.preventDefault();
            console.log('تم النقر على زر الطباعة المحسن');
            optimizedPrint();
        };
    });
}

/**
 * طباعة محسنة
 */
function optimizedPrint() {
    console.log('بدء عملية الطباعة المحسنة');
    
    // إظهار المحتوى الرئيسي بشكل صريح
    showPrintContent();
    
    // إخفاء العناصر غير المرغوبة
    hideNonPrintElements();
    
    // تطبيق تحسينات الطباعة
    applyPrintOptimizations();
    
    // تنفيذ الطباعة بعد تأخير قصير للتأكد من تطبيق التغييرات
    setTimeout(() => {
        console.log('تنفيذ أمر الطباعة...');
        window.print();
        
        // استعادة العناصر بعد الطباعة
        setTimeout(() => {
            console.log('استعادة العناصر بعد الطباعة...');
            restoreNonPrintElements();
            removePrintOptimizations();
        }, 1000);
    }, 300);
}

/**
 * إظهار المحتوى الرئيسي للطباعة بشكل صريح
 */
function showPrintContent() {
    // العناصر الرئيسية التي يجب إظهارها في الطباعة
    const contentSelectors = [
        '.invoice-container',
        '.page-container',
        '.details-table',
        '.order-details',
        '.print-content',
        '.invoice-header',
        '.invoice-body',
        '.invoice-footer',
        '.signature-section'
    ];
    
    contentSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        if (elements.length === 0) {
            console.log(`لم يتم العثور على عناصر بالمحدد: ${selector}`);
        }
        
        elements.forEach(element => {
            console.log(`إظهار عنصر للطباعة: ${selector}`);
            element.style.display = 'block';
            element.style.visibility = 'visible';
            element.style.opacity = '1';
            element.style.position = 'static';
            element.style.overflow = 'visible';
            element.setAttribute('data-print-visible', 'true');
        });
    });
    
    // التأكد من إظهار جداول البيانات بشكل صحيح
    const tables = document.querySelectorAll('table.details-table, table.data-table');
    tables.forEach(table => {
        table.style.display = 'table';
        table.style.visibility = 'visible';
        table.style.opacity = '1';
        
        const rows = table.querySelectorAll('tr');
        rows.forEach(row => {
            row.style.display = 'table-row';
            row.style.visibility = 'visible';
        });
        
        const cells = table.querySelectorAll('td, th');
        cells.forEach(cell => {
            cell.style.display = 'table-cell';
            cell.style.visibility = 'visible';
        });
    });
}

/**
 * إخفاء العناصر غير المرغوبة في الطباعة
 */
function hideNonPrintElements() {
    const elementsToHide = [
        '.action-buttons',
        '.btn:not(.print-btn)',
        'button:not(.print-btn)',
        '.navbar',
        '.sidebar',
        '.pagination',
        '.breadcrumb',
        '.alert',
        '.modal',
        '.dropdown',
        '.tooltip',
        '.popover',
        'nav',
        '.footer-controls',
        '.search-box',
        '.filter-controls',
        '.no-print'
    ];
    
    elementsToHide.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        console.log(`إخفاء ${elements.length} عنصر من النوع: ${selector}`);
        
        elements.forEach(element => {
            // تجنب إخفاء العناصر المهمة للطباعة
            if (element.closest('.print-content') || 
                element.closest('.invoice-container') || 
                element.hasAttribute('data-print-visible')) {
                return;
            }
            
            element.style.display = 'none';
            element.setAttribute('data-hidden-for-print', 'true');
        });
    });
}

/**
 * استعادة العناصر المخفية
 */
function restoreNonPrintElements() {
    const hiddenElements = document.querySelectorAll('[data-hidden-for-print="true"]');
    hiddenElements.forEach(element => {
        element.style.display = '';
        element.removeAttribute('data-hidden-for-print');
    });
}

/**
 * تطبيق تحسينات الطباعة
 */
function applyPrintOptimizations() {
    console.log('تطبيق تحسينات الطباعة...');
    
    // إضافة كلاس للطباعة المحسنة
    document.body.classList.add('print-optimized');
    document.documentElement.classList.add('print-optimized');
    
    // تحسين الحاويات
    const containers = document.querySelectorAll('.container, .invoice-container, .a4-container, .page-container, .print-content');
    console.log(`تحسين ${containers.length} حاوية للطباعة`);
    
    containers.forEach(container => {
        container.style.margin = '0';
        container.style.padding = '5mm';
        container.style.boxShadow = 'none';
        container.style.border = 'none';
        container.style.minHeight = 'auto';
        container.style.maxWidth = 'none';
        container.style.width = '100%';
        container.style.display = 'block';
        container.style.visibility = 'visible';
        container.style.opacity = '1';
        container.style.position = 'static';
        container.style.overflow = 'visible';
        container.style.zIndex = '9999';
        container.setAttribute('data-print-optimized', 'true');
    });
    
    // تحسين الجداول
    const tables = document.querySelectorAll('table');
    console.log(`تحسين ${tables.length} جدول للطباعة`);
    
    tables.forEach(table => {
        table.style.display = 'table';
        table.style.visibility = 'visible';
        table.style.opacity = '1';
        table.style.width = '100%';
        table.style.maxWidth = 'none';
        table.style.pageBreakInside = 'avoid';
        table.style.breakInside = 'avoid';
        table.style.borderCollapse = 'collapse';
        table.style.border = '1px solid #000';
        table.setAttribute('data-print-optimized', 'true');
        
        // تحسين صفوف الجدول
        const rows = table.querySelectorAll('tr');
        rows.forEach(row => {
            row.style.display = 'table-row';
            row.style.visibility = 'visible';
            row.style.pageBreakInside = 'avoid';
        });
        
        // تحسين خلايا الجدول
        const cells = table.querySelectorAll('td, th');
        cells.forEach(cell => {
            cell.style.display = 'table-cell';
            cell.style.visibility = 'visible';
            cell.style.border = '1px solid #000';
            cell.style.padding = '3px';
        });
    });
    
    // تحسين الصور
    const images = document.querySelectorAll('img');
    console.log(`تحسين ${images.length} صورة للطباعة`);
    
    images.forEach(img => {
        img.style.maxWidth = '100%';
        img.style.height = 'auto';
        img.style.pageBreakInside = 'avoid';
        img.style.display = 'inline-block';
        img.style.visibility = 'visible';
        img.style.opacity = '1';
        img.setAttribute('data-print-optimized', 'true');
    });
    
    // تحسين الخطوط
    const textElements = document.querySelectorAll('h1, h2, h3, h4, h5, h6, p, td, th, span, div');
    console.log(`تحسين ${textElements.length} عنصر نصي للطباعة`);
    
    textElements.forEach(element => {
        // تجنب تغيير حجم النص في العناصر المخفية
        if (element.offsetParent === null && !element.closest('.print-content') && !element.closest('.invoice-container')) {
            return;
        }
        
        // تحسين حجم الخط
        try {
            const currentSize = window.getComputedStyle(element).fontSize;
            if (currentSize) {
                const size = parseInt(currentSize);
                if (!isNaN(size)) {
                    // الحفاظ على حجم الخط مناسب للطباعة
                    const newSize = Math.max(8, size) + 'px';
                    element.style.fontSize = newSize;
                    element.style.lineHeight = '1.3';
                }
            }
        } catch (e) {
            console.error('خطأ في تحسين حجم الخط:', e);
        }
        
        // تحسين لون النص للطباعة
        element.style.color = '#000';
        element.style.textShadow = 'none';
        element.setAttribute('data-print-optimized', 'true');
    });
    
    // إضافة ورقة أنماط مؤقتة للطباعة
    const printStyle = document.createElement('style');
    printStyle.id = 'temp-print-styles';
    printStyle.textContent = `
        @media print {
            @page {
                size: A4 portrait;
                margin: 10mm 15mm;
            }
            
            html, body {
                width: 210mm;
                height: 297mm;
                margin: 0 !important;
                padding: 0 !important;
                background: white !important;
                color: black !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
                color-adjust: exact !important;
            }
            
            * {
                visibility: visible !important;
                overflow: visible !important;
            }
            
            .print-content, .invoice-container, .page-container, .details-table {
                display: block !important;
                visibility: visible !important;
                opacity: 1 !important;
                position: static !important;
                overflow: visible !important;
                background: white !important;
            }
            
            table {
                display: table !important;
                width: 100% !important;
                border-collapse: collapse !important;
            }
            
            tr {
                display: table-row !important;
            }
            
            td, th {
                display: table-cell !important;
                border: 1px solid #000 !important;
                padding: 3px !important;
            }
        }
    `;
    document.head.appendChild(printStyle);
}

/**
 * إزالة تحسينات الطباعة
 */
function removePrintOptimizations() {
    console.log('إزالة تحسينات الطباعة...');
    
    // إزالة الأنماط من العناصر
    document.body.classList.remove('print-optimized');
    document.documentElement.classList.remove('print-optimized');
    
    // إزالة ورقة الأنماط المؤقتة
    const tempStyle = document.getElementById('temp-print-styles');
    if (tempStyle) {
        tempStyle.remove();
    }
    
    // استعادة العناصر المحسنة
    const optimizedElements = document.querySelectorAll('[data-print-optimized="true"]');
    console.log(`استعادة ${optimizedElements.length} عنصر محسن`);
    
    optimizedElements.forEach(element => {
        element.style.margin = '';
        element.style.padding = '';
        element.style.boxShadow = '';
        element.style.border = '';
        element.style.minHeight = '';
        element.style.maxWidth = '';
        element.style.width = '';
        element.style.pageBreakInside = '';
        element.style.breakInside = '';
        element.style.maxWidth = '';
        element.style.height = '';
        element.style.fontSize = '';
        element.style.lineHeight = '';
        element.style.color = '';
        element.style.textShadow = '';
        element.style.borderCollapse = '';
        element.style.zIndex = '';
        element.removeAttribute('data-print-optimized');
    });
    
    // استعادة العناصر المرئية للطباعة
    const visibleElements = document.querySelectorAll('[data-print-visible="true"]');
    console.log(`استعادة ${visibleElements.length} عنصر مرئي للطباعة`);
    
    visibleElements.forEach(element => {
        element.style.display = '';
        element.style.visibility = '';
        element.style.opacity = '';
        element.style.position = '';
        element.style.overflow = '';
        element.removeAttribute('data-print-visible');
    });
}

/**
 * تحسين تخطيط الطباعة
 */
function optimizePrintLayout() {
    console.log('تطبيق تخطيط الطباعة المحسن...');
    
    // إضافة CSS للطباعة المحسنة - تحسين لورقة A4
    const printCSS = `
        @media print {
            @page {
                size: A4 portrait;
                margin: 10mm 15mm;
            }
            
            html, body {
                width: 210mm !important;
                height: 297mm !important;
                margin: 0 !important;
                padding: 0 !important;
                background: white !important;
                color: black !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
                color-adjust: exact !important;
                overflow: hidden !important;
            }
            
            /* إخفاء العناصر غير المرغوبة */
            .action-buttons, .btn:not(.print-btn), button:not(.print-btn),
            .navbar, .sidebar, .pagination, .breadcrumb, .alert,
            .modal, .dropdown, .tooltip, .popover, nav,
            .footer-controls, .search-box, .filter-controls, .no-print {
                display: none !important;
                visibility: hidden !important;
            }
            
            /* إظهار المحتوى الرئيسي */
            .print-content, .invoice-container, .page-container, .details-table,
            .order-details, .invoice-header, .invoice-body, .invoice-footer,
            .signature-section {
                display: block !important;
                visibility: visible !important;
                opacity: 1 !important;
                position: static !important;
                overflow: visible !important;
                background: white !important;
                margin: 0 !important;
                padding: 5mm !important;
                box-shadow: none !important;
                border: none !important;
                min-height: auto !important;
                max-width: none !important;
                width: 100% !important;
                z-index: 9999 !important;
            }
            
            /* تحسين الجداول */
            table {
                display: table !important;
                width: 100% !important;
                border-collapse: collapse !important;
                page-break-inside: avoid !important;
                break-inside: avoid !important;
                border: 1px solid #000 !important;
                margin-bottom: 10mm !important;
            }
            
            tr {
                display: table-row !important;
                visibility: visible !important;
                page-break-inside: avoid !important;
            }
            
            td, th {
                display: table-cell !important;
                visibility: visible !important;
                border: 1px solid #000 !important;
                padding: 3px !important;
            }
            
            /* تحسين الصور */
            img {
                max-width: 100% !important;
                height: auto !important;
                page-break-inside: avoid !important;
                display: inline-block !important;
                visibility: visible !important;
                opacity: 1 !important;
            }
            
            /* تحسين النصوص */
            h1, h2, h3, h4, h5, h6, p, td, th, span, div {
                color: #000 !important;
                text-shadow: none !important;
                min-height: auto !important;
            }
            
            /* تحسين أقسام التوقيع */
            .signature-section {
                margin-top: 10mm !important;
                page-break-inside: avoid !important;
            }
            
            .signature-line {
                height: 8mm !important;
                margin-bottom: 3mm !important;
                border-bottom: 1px solid #000 !important;
            }
            
            .stamp-circle {
                width: 20mm !important;
                height: 20mm !important;
                border: 1px dashed #000 !important;
                border-radius: 50% !important;
                margin: 5mm auto !important;
            }
        }
    `;
    
    // إضافة CSS إلى الصفحة
    const style = document.createElement('style');
    style.textContent = printCSS;
    style.id = 'print-optimization-css';
    document.head.appendChild(style);
    
    // إضافة كلاس للطباعة على عنصر html
    document.documentElement.classList.add('print-ready');
}

/**
 * إضافة مستمعات الأحداث
 */
function addPrintEventListeners() {
    // مستمع لبداية الطباعة
    window.addEventListener('beforeprint', function() {
        console.log('بدء الطباعة المحسنة تلقائياً...');
        // إظهار المحتوى الرئيسي بشكل صريح
        showPrintContent();
        // تطبيق تحسينات الطباعة
        applyPrintOptimizations();
    });
    
    // مستمع لنهاية الطباعة
    window.addEventListener('afterprint', function() {
        console.log('انتهاء الطباعة المحسنة...');
        setTimeout(() => {
            removePrintOptimizations();
        }, 500);
    });
    
    // مستمع لتغيير حجم النافذة
    window.addEventListener('resize', function() {
        if (window.innerWidth < 768) {
            // تحسينات للشاشات الصغيرة
            optimizeForMobile();
        }
    });
    
    // مستمع لأزرار الطباعة المضافة ديناميكياً
    document.addEventListener('click', function(e) {
        // التحقق من أن العنصر المنقور عليه هو زر طباعة
        if (e.target && (
            e.target.classList.contains('print-btn') || 
            (e.target.tagName === 'BUTTON' && e.target.innerHTML.includes('print')) ||
            (e.target.tagName === 'I' && e.target.classList.contains('fa-print')) ||
            e.target.closest('button[onclick*="print"]')
        )) {
            e.preventDefault();
            console.log('تم اكتشاف نقرة على زر طباعة');
            optimizedPrint();
        }
    });
}

/**
 * تحسينات للشاشات الصغيرة
 */
function optimizeForMobile() {
    const containers = document.querySelectorAll('.a4-container, .invoice-container, .page-container, .print-content');
    containers.forEach(container => {
        container.style.width = '100%';
        container.style.margin = '10px';
        container.style.padding = '15px';
        container.style.overflow = 'auto';
    });
    
    // تحسين الجداول للشاشات الصغيرة
    const tables = document.querySelectorAll('table.details-table, table.data-table');
    tables.forEach(table => {
        table.style.width = '100%';
        table.style.fontSize = '12px';
    });
}

/**
 * طباعة سريعة محسنة
 */
function quickOptimizedPrint() {
    console.log('بدء الطباعة السريعة المحسنة...');
    
    // إظهار المحتوى الرئيسي فقط
    const contentSelectors = ['.invoice-container', '.page-container', '.details-table'];
    contentSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
            element.style.display = 'block';
            element.style.visibility = 'visible';
            element.style.opacity = '1';
        });
    });
    
    // تطبيق تحسينات سريعة
    document.body.style.fontSize = '10px';
    document.body.style.lineHeight = '1.2';
    document.body.style.background = 'white';
    document.body.style.color = 'black';
    
    // إضافة ورقة أنماط مؤقتة للطباعة السريعة
    const quickStyle = document.createElement('style');
    quickStyle.id = 'quick-print-style';
    quickStyle.textContent = `
        @media print {
            @page { margin: 10mm; }
            body * { visibility: hidden; }
            .invoice-container, .invoice-container *, .page-container, .page-container *, .details-table, .details-table * {
                visibility: visible !important;
                display: block !important;
            }
            table { display: table !important; }
            tr { display: table-row !important; }
            td, th { display: table-cell !important; }
        }
    `;
    document.head.appendChild(quickStyle);
    
    // طباعة فورية
    setTimeout(() => {
        window.print();
        
        // استعادة الإعدادات
        setTimeout(() => {
            document.body.style.fontSize = '';
            document.body.style.lineHeight = '';
            document.body.style.background = '';
            document.body.style.color = '';
            
            // إزالة ورقة الأنماط المؤقتة
            const style = document.getElementById('quick-print-style');
            if (style) {
                style.remove();
            }
        }, 1000);
    }, 100);
}

/**
 * طباعة محسنة للفواتير
 */
function printInvoice() {
    console.log('بدء طباعة الفاتورة المحسنة...');
    
    // إضافة كلاس للطباعة
    document.body.classList.add('invoice-print-mode');
    
    // إظهار محتوى الفاتورة فقط
    const invoiceContainer = document.querySelector('.invoice-container');
    if (invoiceContainer) {
        // إخفاء جميع العناصر
        const allElements = document.querySelectorAll('body > *');
        allElements.forEach(el => {
            if (el !== invoiceContainer && !invoiceContainer.contains(el)) {
                el.style.display = 'none';
            }
        });
        
        // إظهار الفاتورة
        invoiceContainer.style.display = 'block';
        invoiceContainer.style.visibility = 'visible';
        invoiceContainer.style.opacity = '1';
        invoiceContainer.style.position = 'absolute';
        invoiceContainer.style.top = '0';
        invoiceContainer.style.left = '0';
        invoiceContainer.style.width = '100%';
        invoiceContainer.style.background = 'white';
        invoiceContainer.style.zIndex = '9999';
        invoiceContainer.style.padding = '10mm';
        invoiceContainer.style.boxSizing = 'border-box';
    }
    
    // طباعة
    setTimeout(() => {
        window.print();
        
        // استعادة العناصر
        setTimeout(() => {
            document.body.classList.remove('invoice-print-mode');
            
            if (invoiceContainer) {
                // استعادة العناصر المخفية
                const allElements = document.querySelectorAll('body > *');
                allElements.forEach(el => {
                    el.style.display = '';
                });
                
                // استعادة خصائص الفاتورة
                invoiceContainer.style.position = '';
                invoiceContainer.style.top = '';
                invoiceContainer.style.left = '';
                invoiceContainer.style.width = '';
                invoiceContainer.style.background = '';
                invoiceContainer.style.zIndex = '';
                invoiceContainer.style.padding = '';
                invoiceContainer.style.boxSizing = '';
            }
        }, 1000);
    }, 300);
}

// تصدير الوظائف للاستخدام العام
window.optimizedPrint = optimizedPrint;
window.quickOptimizedPrint = quickOptimizedPrint;
window.printInvoice = printInvoice;
